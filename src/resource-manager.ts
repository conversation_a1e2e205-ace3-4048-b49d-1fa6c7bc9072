import { v4 as uuidv4 } from 'uuid';
import { CAFS } from './cafs';
import { 
    Resource, 
    ResourceId, 
    ResourceType, 
    ResourceMetadata, 
    JobStep, 
    Job,
    GCSUtilsConfig,
    IntegerInstance 
} from './types';

/**
 * Resource Manager for handling Resource creation, storage, and retrieval
 * Integrates with CAFS for content-addressable storage
 */
export class ResourceManager {
    private cafs: CAFS;
    private config: GCSUtilsConfig;

    constructor(config: Partial<GCSUtilsConfig> = {}) {
        this.config = {
            bucketName: config.bucketName || process.env.BUCKET_NAME || 'tp-resources',
            metadataCollection: config.metadataCollection || 'resources_metadata',
            enableDeduplication: config.enableDeduplication ?? true,
            maxFileSize: config.maxFileSize || 10 * 1024 * 1024,
            defaultContentType: config.defaultContentType || 'application/json'
        };
        
        this.cafs = new CAFS(this.config);
    }

    /**
     * Creates a new Resource with CAFS integration
     * @param content The content of the resource
     * @param resourceType The type of the resource
     * @param jobStep The job step creating this resource
     * @param job The job containing the job step
     * @param metadata Optional additional metadata
     * @returns The created Resource
     */
    async createResource<T = any>(
        content: T,
        resourceType: ResourceType,
        jobStep: JobStep,
        job: Job,
        metadata: Partial<ResourceMetadata> = {}
    ): Promise<Resource<T>> {
        try {
            // Serialize content for storage
            const serializedContent = this.serializeContent(content, resourceType);
            
            // Store content in CAFS
            const cafsResult = await this.cafs.storeContent(serializedContent, {
                contentType: this.getContentType(resourceType),
                tags: metadata.tags || [resourceType, jobStep.id, job.id],
                customProperties: {
                    resourceType,
                    jobStepId: jobStep.id,
                    jobId: job.id,
                    ...metadata.customProperties
                }
            });

            if (!cafsResult.success) {
                throw new Error(cafsResult.error || 'Failed to store content in CAFS');
            }

            // Create ResourceId
            const resourceId: ResourceId = {
                id: uuidv4(),
                resourceType,
                jobStepId: jobStep.id,
                jobId: job.id,
                createdAt: new Date()
            };

            // Create Resource metadata
            const resourceMetadata: ResourceMetadata = {
                contentSize: Buffer.byteLength(serializedContent, 'utf8'),
                contentType: this.getContentType(resourceType),
                createdAt: new Date(),
                lastAccessedAt: new Date(),
                referenceCount: 1,
                tags: metadata.tags || [resourceType, jobStep.id, job.id],
                customProperties: {
                    resourceType,
                    jobStepId: jobStep.id,
                    jobId: job.id,
                    deduplicated: cafsResult.deduplicated,
                    ...metadata.customProperties
                }
            };

            // Create Resource
            const resource: Resource<T> = {
                resourceId,
                contentHash: cafsResult.contentHash,
                content,
                metadata: resourceMetadata
            };

            // Store resource metadata
            await this.storeResourceMetadata(resource);

            // Update job step with created resource
            await this.updateJobStepResources(jobStep.id, resourceId.id);

            return resource;

        } catch (error) {
            throw new Error(`Failed to create resource: ${error}`);
        }
    }

    /**
     * Retrieves a Resource by its ID
     * @param resourceId The resource ID
     * @returns The Resource or null if not found
     */
    async getResource<T = any>(resourceId: string): Promise<Resource<T> | null> {
        try {
            // Get resource metadata
            const resourceMetadata = await this.getResourceMetadata(resourceId);
            if (!resourceMetadata) {
                return null;
            }

            // Retrieve content from CAFS
            const serializedContent = await this.cafs.retrieveContent(resourceMetadata.contentHash);
            
            // Deserialize content
            const content = this.deserializeContent<T>(
                serializedContent, 
                resourceMetadata.resourceId.resourceType
            );

            // Update last accessed time
            resourceMetadata.metadata.lastAccessedAt = new Date();
            await this.updateResourceMetadata(resourceMetadata.resourceId.id, {
                lastAccessedAt: new Date()
            });

            return {
                ...resourceMetadata,
                content
            };

        } catch (error) {
            throw new Error(`Failed to retrieve resource ${resourceId}: ${error}`);
        }
    }

    /**
     * Retrieves a Resource by its content hash
     * @param contentHash The SHA-256 content hash
     * @returns The Resource or null if not found
     */
    async getResourceByHash<T = any>(contentHash: string): Promise<Resource<T> | null> {
        try {
            // Find resource by content hash
            const resourceMetadata = await this.findResourceByHash(contentHash);
            if (!resourceMetadata) {
                return null;
            }

            return await this.getResource<T>(resourceMetadata.resourceId.id);

        } catch (error) {
            throw new Error(`Failed to retrieve resource by hash ${contentHash}: ${error}`);
        }
    }

    /**
     * Creates an integer Resource (specific implementation for the current system)
     * @param value The integer value
     * @param jobStep The job step creating this resource
     * @param job The job containing the job step
     * @returns The created Resource
     */
    async createIntegerResource(
        value: number,
        jobStep: JobStep,
        job: Job
    ): Promise<Resource<IntegerInstance>> {
        const integerInstance: IntegerInstance = { semanticIdentity: value };
        
        return await this.createResource(
            integerInstance,
            'integer',
            jobStep,
            job,
            {
                tags: ['integer', 'semantic-identity'],
                customProperties: {
                    integerValue: value
                }
            }
        );
    }

    /**
     * Finds existing Resources with the same content
     * @param content The content to search for
     * @param resourceType The resource type
     * @returns Array of Resources with matching content
     */
    async findResourcesByContent<T = any>(
        content: T,
        resourceType: ResourceType
    ): Promise<Resource<T>[]> {
        try {
            const serializedContent = this.serializeContent(content, resourceType);
            const contentHash = await this.cafs['generateContentHash'](serializedContent);
            
            // Check if content exists in CAFS
            const exists = await this.cafs.contentExists(contentHash);
            if (!exists) {
                return [];
            }

            // Find all resources with this content hash
            const resources = await this.findResourcesByHash(contentHash);
            
            // Return resources with deserialized content
            return Promise.all(
                resources.map(async (resourceMeta) => {
                    const resource = await this.getResource<T>(resourceMeta.resourceId.id);
                    return resource!;
                })
            );

        } catch (error) {
            throw new Error(`Failed to find resources by content: ${error}`);
        }
    }

    /**
     * Deletes a Resource
     * @param resourceId The resource ID to delete
     * @param forceDelete Whether to force delete from CAFS
     */
    async deleteResource(resourceId: string, forceDelete: boolean = false): Promise<void> {
        try {
            const resourceMetadata = await this.getResourceMetadata(resourceId);
            if (!resourceMetadata) {
                throw new Error(`Resource ${resourceId} not found`);
            }

            // Delete from CAFS (will handle reference counting)
            await this.cafs.deleteContent(resourceMetadata.contentHash, forceDelete);

            // Delete resource metadata
            await this.deleteResourceMetadata(resourceId);

        } catch (error) {
            throw new Error(`Failed to delete resource ${resourceId}: ${error}`);
        }
    }

    /**
     * Lists Resources by job or job step
     * @param jobId Optional job ID filter
     * @param jobStepId Optional job step ID filter
     * @returns Array of Resource metadata
     */
    async listResources(jobId?: string, jobStepId?: string): Promise<Resource[]> {
        // In a real implementation, this would query Firestore with filters
        // For now, return empty array as placeholder
        return [];
    }

    /**
     * Serializes content for storage
     * @param content The content to serialize
     * @param resourceType The resource type
     * @returns Serialized content string
     */
    private serializeContent<T>(content: T, resourceType: ResourceType): string {
        switch (resourceType) {
            case 'integer':
            case 'object':
            case 'array':
                return JSON.stringify(content, null, 2);
            case 'string':
                return typeof content === 'string' ? content : String(content);
            default:
                return JSON.stringify(content, null, 2);
        }
    }

    /**
     * Deserializes content from storage
     * @param serializedContent The serialized content
     * @param resourceType The resource type
     * @returns Deserialized content
     */
    private deserializeContent<T>(serializedContent: string, resourceType: ResourceType): T {
        switch (resourceType) {
            case 'integer':
            case 'object':
            case 'array':
                return JSON.parse(serializedContent) as T;
            case 'string':
                return serializedContent as T;
            default:
                return JSON.parse(serializedContent) as T;
        }
    }

    /**
     * Gets content type for resource type
     * @param resourceType The resource type
     * @returns MIME content type
     */
    private getContentType(resourceType: ResourceType): string {
        switch (resourceType) {
            case 'string':
                return 'text/plain';
            case 'integer':
            case 'object':
            case 'array':
                return 'application/json';
            default:
                return 'application/json';
        }
    }

    // Placeholder methods for metadata storage (would use Firestore in real implementation)
    private async storeResourceMetadata(resource: Omit<Resource, 'content'>): Promise<void> {
        // Store in GCS as JSON for now
        const metadataPath = `resources/metadata/${resource.resourceId.id}.json`;
        const metadataContent = JSON.stringify(resource, null, 2);
        
        await this.cafs['gcsUtils'].writeRawContent(metadataPath, metadataContent, 'application/json');
    }

    private async getResourceMetadata(resourceId: string): Promise<Omit<Resource, 'content'> | null> {
        try {
            const metadataPath = `resources/metadata/${resourceId}.json`;
            const metadataContent = await this.cafs['gcsUtils'].readRawContent(metadataPath);
            return JSON.parse(metadataContent);
        } catch (error) {
            return null;
        }
    }

    private async updateResourceMetadata(resourceId: string, updates: Partial<ResourceMetadata>): Promise<void> {
        // In real implementation, would update in Firestore
        // For now, read, update, and write back
        try {
            const existing = await this.getResourceMetadata(resourceId);
            if (existing) {
                existing.metadata = { ...existing.metadata, ...updates };
                await this.storeResourceMetadata(existing);
            }
        } catch (error) {
            // Ignore errors for now
        }
    }

    private async deleteResourceMetadata(resourceId: string): Promise<void> {
        const metadataPath = `resources/metadata/${resourceId}.json`;
        await this.cafs['gcsUtils'].deleteFile(metadataPath);
    }

    private async findResourceByHash(contentHash: string): Promise<Omit<Resource, 'content'> | null> {
        // In real implementation, would query Firestore by contentHash
        // For now, return null as placeholder
        return null;
    }

    private async findResourcesByHash(contentHash: string): Promise<Omit<Resource, 'content'>[]> {
        // In real implementation, would query Firestore by contentHash
        // For now, return empty array as placeholder
        return [];
    }

    private async updateJobStepResources(jobStepId: string, resourceId: string): Promise<void> {
        // In real implementation, would update JobStep in database
        // For now, this is a placeholder
    }
}
