/**
 * GCS Utils SDK - TypeScript SDK for Google Cloud Storage with CAFS support
 * 
 * This SDK provides:
 * - Content Addressable File Storage (CAFS) with deduplication
 * - Resource management with metadata tracking
 * - Integration with Google Cloud Storage and Firestore
 * - Support for various resource types (integer, string, object, array)
 */

// Core exports
export { GCSUtils } from './gcs-utils';
export { CAFS } from './cafs';
export { ResourceManager } from './resource-manager';
export { FirestoreAdapter } from './firestore-adapter';

// Type exports
export * from './types';

// Re-export the original functions for backward compatibility
export { GCSUtils as default } from './gcs-utils';

/**
 * Convenience function to create a GCSUtils instance
 * @param bucketName Optional bucket name
 * @returns GCSUtils instance
 */
export function createGCSUtils(bucketName?: string): GCSUtils {
    return new GCSUtils(bucketName);
}

/**
 * Convenience function to create a CAFS instance
 * @param config Optional configuration
 * @returns CAFS instance
 */
export function createCAFS(config?: any) {
    const { CAFS } = require('./cafs');
    return new CAFS(config);
}

/**
 * Convenience function to create a ResourceManager instance
 * @param config Optional configuration
 * @returns ResourceManager instance
 */
export function createResourceManager(config?: any) {
    const { ResourceManager } = require('./resource-manager');
    return new ResourceManager(config);
}

/**
 * Convenience function to create a FirestoreAdapter instance
 * @param projectId Optional project ID
 * @param collections Optional collection names
 * @returns FirestoreAdapter instance
 */
export function createFirestoreAdapter(
    projectId?: string,
    collections?: {
        resources?: string;
        cafs?: string;
        jobs?: string;
        jobSteps?: string;
    }
) {
    const { FirestoreAdapter } = require('./firestore-adapter');
    return new FirestoreAdapter(
        projectId,
        collections?.resources,
        collections?.cafs,
        collections?.jobs,
        collections?.jobSteps
    );
}

// Legacy exports for backward compatibility with the original gcs-utils.ts
import { GCSUtils } from './gcs-utils';

const defaultGCSUtils = new GCSUtils();

/**
 * Legacy function: Reads a number value from a Google Cloud Storage file
 * @deprecated Use GCSUtils class instead
 */
export async function readFromGCS(filePath: string): Promise<number> {
    return defaultGCSUtils.readFromGCS(filePath);
}

/**
 * Legacy function: Writes a number value to a Google Cloud Storage file
 * @deprecated Use GCSUtils class instead
 */
export async function writeToGCS(filePath: string, semanticIdentity: number): Promise<void> {
    return defaultGCSUtils.writeToGCS(filePath, semanticIdentity);
}
