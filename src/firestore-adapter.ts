import { Firestore, FieldValue } from '@google-cloud/firestore';
import { 
    Resource, 
    CAFSEntry, 
    JobStep, 
    Job,
    ResourceMetadata 
} from './types';

/**
 * Firestore adapter for storing Resource and CAFS metadata
 * Provides persistent storage for metadata while content is stored in CAFS
 */
export class FirestoreAdapter {
    private firestore: Firestore;
    private resourcesCollection: string;
    private cafsCollection: string;
    private jobsCollection: string;
    private jobStepsCollection: string;

    constructor(
        projectId?: string,
        resourcesCollection: string = 'resources',
        cafsCollection: string = 'cafs_entries',
        jobsCollection: string = 'jobs',
        jobStepsCollection: string = 'job_steps'
    ) {
        this.firestore = new Firestore({ projectId });
        this.resourcesCollection = resourcesCollection;
        this.cafsCollection = cafsCollection;
        this.jobsCollection = jobsCollection;
        this.jobStepsCollection = jobStepsCollection;
    }

    // Resource operations
    
    /**
     * Stores Resource metadata in Firestore
     * @param resource The Resource to store
     */
    async storeResourceMetadata(resource: Omit<Resource, 'content'>): Promise<void> {
        try {
            const docRef = this.firestore.collection(this.resourcesCollection).doc(resource.resourceId.id);
            await docRef.set({
                resourceId: resource.resourceId,
                contentHash: resource.contentHash,
                metadata: {
                    ...resource.metadata,
                    createdAt: resource.metadata.createdAt,
                    lastAccessedAt: resource.metadata.lastAccessedAt
                }
            });
        } catch (error) {
            throw new Error(`Failed to store resource metadata: ${error}`);
        }
    }

    /**
     * Retrieves Resource metadata from Firestore
     * @param resourceId The resource ID
     * @returns Resource metadata or null if not found
     */
    async getResourceMetadata(resourceId: string): Promise<Omit<Resource, 'content'> | null> {
        try {
            const docRef = this.firestore.collection(this.resourcesCollection).doc(resourceId);
            const doc = await docRef.get();
            
            if (!doc.exists) {
                return null;
            }

            const data = doc.data()!;
            return {
                resourceId: {
                    ...data.resourceId,
                    createdAt: data.resourceId.createdAt.toDate()
                },
                contentHash: data.contentHash,
                metadata: {
                    ...data.metadata,
                    createdAt: data.metadata.createdAt.toDate(),
                    lastAccessedAt: data.metadata.lastAccessedAt.toDate()
                }
            };
        } catch (error) {
            throw new Error(`Failed to get resource metadata: ${error}`);
        }
    }

    /**
     * Updates Resource metadata in Firestore
     * @param resourceId The resource ID
     * @param updates Partial metadata updates
     */
    async updateResourceMetadata(
        resourceId: string, 
        updates: Partial<ResourceMetadata>
    ): Promise<void> {
        try {
            const docRef = this.firestore.collection(this.resourcesCollection).doc(resourceId);
            await docRef.update({
                [`metadata.${Object.keys(updates).join('`], [`metadata.')}`]: Object.values(updates)
            });
        } catch (error) {
            throw new Error(`Failed to update resource metadata: ${error}`);
        }
    }

    /**
     * Deletes Resource metadata from Firestore
     * @param resourceId The resource ID
     */
    async deleteResourceMetadata(resourceId: string): Promise<void> {
        try {
            const docRef = this.firestore.collection(this.resourcesCollection).doc(resourceId);
            await docRef.delete();
        } catch (error) {
            throw new Error(`Failed to delete resource metadata: ${error}`);
        }
    }

    /**
     * Finds Resources by content hash
     * @param contentHash The content hash
     * @returns Array of Resource metadata
     */
    async findResourcesByContentHash(contentHash: string): Promise<Omit<Resource, 'content'>[]> {
        try {
            const querySnapshot = await this.firestore
                .collection(this.resourcesCollection)
                .where('contentHash', '==', contentHash)
                .get();

            const resources: Omit<Resource, 'content'>[] = [];
            querySnapshot.forEach(doc => {
                const data = doc.data();
                resources.push({
                    resourceId: {
                        ...data.resourceId,
                        createdAt: data.resourceId.createdAt.toDate()
                    },
                    contentHash: data.contentHash,
                    metadata: {
                        ...data.metadata,
                        createdAt: data.metadata.createdAt.toDate(),
                        lastAccessedAt: data.metadata.lastAccessedAt.toDate()
                    }
                });
            });

            return resources;
        } catch (error) {
            throw new Error(`Failed to find resources by content hash: ${error}`);
        }
    }

    /**
     * Lists Resources with optional filters
     * @param jobId Optional job ID filter
     * @param jobStepId Optional job step ID filter
     * @param resourceType Optional resource type filter
     * @returns Array of Resource metadata
     */
    async listResources(
        jobId?: string,
        jobStepId?: string,
        resourceType?: string
    ): Promise<Omit<Resource, 'content'>[]> {
        try {
            let query = this.firestore.collection(this.resourcesCollection);

            if (jobId) {
                query = query.where('resourceId.jobId', '==', jobId) as any;
            }
            if (jobStepId) {
                query = query.where('resourceId.jobStepId', '==', jobStepId) as any;
            }
            if (resourceType) {
                query = query.where('resourceId.resourceType', '==', resourceType) as any;
            }

            const querySnapshot = await query.get();
            const resources: Omit<Resource, 'content'>[] = [];

            querySnapshot.forEach(doc => {
                const data = doc.data();
                resources.push({
                    resourceId: {
                        ...data.resourceId,
                        createdAt: data.resourceId.createdAt.toDate()
                    },
                    contentHash: data.contentHash,
                    metadata: {
                        ...data.metadata,
                        createdAt: data.metadata.createdAt.toDate(),
                        lastAccessedAt: data.metadata.lastAccessedAt.toDate()
                    }
                });
            });

            return resources;
        } catch (error) {
            throw new Error(`Failed to list resources: ${error}`);
        }
    }

    // CAFS operations

    /**
     * Stores CAFS entry in Firestore
     * @param entry The CAFS entry to store
     */
    async storeCAFSEntry(entry: CAFSEntry): Promise<void> {
        try {
            const docRef = this.firestore.collection(this.cafsCollection).doc(entry.contentHash);
            await docRef.set({
                ...entry,
                metadata: {
                    ...entry.metadata,
                    createdAt: entry.metadata.createdAt,
                    lastAccessedAt: entry.metadata.lastAccessedAt
                }
            });
        } catch (error) {
            throw new Error(`Failed to store CAFS entry: ${error}`);
        }
    }

    /**
     * Retrieves CAFS entry from Firestore
     * @param contentHash The content hash
     * @returns CAFS entry or null if not found
     */
    async getCAFSEntry(contentHash: string): Promise<CAFSEntry | null> {
        try {
            const docRef = this.firestore.collection(this.cafsCollection).doc(contentHash);
            const doc = await docRef.get();
            
            if (!doc.exists) {
                return null;
            }

            const data = doc.data()!;
            return {
                ...data,
                metadata: {
                    ...data.metadata,
                    createdAt: data.metadata.createdAt.toDate(),
                    lastAccessedAt: data.metadata.lastAccessedAt.toDate()
                }
            } as CAFSEntry;
        } catch (error) {
            throw new Error(`Failed to get CAFS entry: ${error}`);
        }
    }

    /**
     * Updates CAFS entry in Firestore
     * @param contentHash The content hash
     * @param updates Partial CAFS entry updates
     */
    async updateCAFSEntry(contentHash: string, updates: Partial<CAFSEntry>): Promise<void> {
        try {
            const docRef = this.firestore.collection(this.cafsCollection).doc(contentHash);
            await docRef.update(updates);
        } catch (error) {
            throw new Error(`Failed to update CAFS entry: ${error}`);
        }
    }

    /**
     * Deletes CAFS entry from Firestore
     * @param contentHash The content hash
     */
    async deleteCAFSEntry(contentHash: string): Promise<void> {
        try {
            const docRef = this.firestore.collection(this.cafsCollection).doc(contentHash);
            await docRef.delete();
        } catch (error) {
            throw new Error(`Failed to delete CAFS entry: ${error}`);
        }
    }

    /**
     * Lists all CAFS entries
     * @returns Array of CAFS entries
     */
    async listCAFSEntries(): Promise<CAFSEntry[]> {
        try {
            const querySnapshot = await this.firestore.collection(this.cafsCollection).get();
            const entries: CAFSEntry[] = [];

            querySnapshot.forEach(doc => {
                const data = doc.data();
                entries.push({
                    ...data,
                    metadata: {
                        ...data.metadata,
                        createdAt: data.metadata.createdAt.toDate(),
                        lastAccessedAt: data.metadata.lastAccessedAt.toDate()
                    }
                } as CAFSEntry);
            });

            return entries;
        } catch (error) {
            throw new Error(`Failed to list CAFS entries: ${error}`);
        }
    }

    // Job and JobStep operations (for integration)

    /**
     * Stores Job in Firestore
     * @param job The Job to store
     */
    async storeJob(job: Job): Promise<void> {
        try {
            const docRef = this.firestore.collection(this.jobsCollection).doc(job.id);
            await docRef.set({
                ...job,
                createdAt: job.createdAt
            });
        } catch (error) {
            throw new Error(`Failed to store job: ${error}`);
        }
    }

    /**
     * Retrieves Job from Firestore
     * @param jobId The job ID
     * @returns Job or null if not found
     */
    async getJob(jobId: string): Promise<Job | null> {
        try {
            const docRef = this.firestore.collection(this.jobsCollection).doc(jobId);
            const doc = await docRef.get();
            
            if (!doc.exists) {
                return null;
            }

            const data = doc.data()!;
            return {
                ...data,
                createdAt: data.createdAt.toDate()
            } as Job;
        } catch (error) {
            throw new Error(`Failed to get job: ${error}`);
        }
    }

    /**
     * Stores JobStep in Firestore
     * @param jobStep The JobStep to store
     */
    async storeJobStep(jobStep: JobStep): Promise<void> {
        try {
            const docRef = this.firestore.collection(this.jobStepsCollection).doc(jobStep.id);
            await docRef.set(jobStep);
        } catch (error) {
            throw new Error(`Failed to store job step: ${error}`);
        }
    }

    /**
     * Retrieves JobStep from Firestore
     * @param jobStepId The job step ID
     * @returns JobStep or null if not found
     */
    async getJobStep(jobStepId: string): Promise<JobStep | null> {
        try {
            const docRef = this.firestore.collection(this.jobStepsCollection).doc(jobStepId);
            const doc = await docRef.get();
            
            if (!doc.exists) {
                return null;
            }

            return doc.data() as JobStep;
        } catch (error) {
            throw new Error(`Failed to get job step: ${error}`);
        }
    }

    /**
     * Updates JobStep with created resource
     * @param jobStepId The job step ID
     * @param resourceId The resource ID to add
     */
    async addResourceToJobStep(jobStepId: string, resourceId: string): Promise<void> {
        try {
            const docRef = this.firestore.collection(this.jobStepsCollection).doc(jobStepId);
            await docRef.update({
                createdResources: FieldValue.arrayUnion(resourceId)
            });
        } catch (error) {
            throw new Error(`Failed to add resource to job step: ${error}`);
        }
    }
}
