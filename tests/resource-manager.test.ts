import { ResourceManager } from '../src/resource-manager';
import { CAFS } from '../src/cafs';
import { ResourceType, JobStep, Job, IntegerInstance } from '../src/types';

// Mock CAFS
jest.mock('../src/cafs');
const MockedCAFS = CAFS as jest.MockedClass<typeof CAFS>;

describe('ResourceManager', () => {
    let resourceManager: ResourceManager;
    let mockCAFS: jest.Mocked<CAFS>;

    const mockJobStep: JobStep = {
        id: 'step-123',
        jobId: 'job-456',
        name: 'Test Step',
        status: 'running',
        createdResources: []
    };

    const mockJob: Job = {
        id: 'job-456',
        name: 'Test Job',
        status: 'running',
        steps: [mockJobStep],
        createdAt: new Date()
    };

    beforeEach(() => {
        jest.clearAllMocks();
        
        // Create mock CAFS instance
        mockCAFS = {
            storeContent: jest.fn(),
            retrieveContent: jest.fn(),
            contentExists: jest.fn(),
            deleteContent: jest.fn(),
            getCAFSEntry: jest.fn()
        } as any;

        MockedCAFS.mockImplementation(() => mockCAFS);
        
        resourceManager = new ResourceManager({
            bucketName: 'test-bucket',
            enableDeduplication: true
        });
    });

    describe('createResource', () => {
        it('should create a new resource successfully', async () => {
            const content = { data: 'test' };
            const resourceType: ResourceType = 'object';
            const contentHash = 'abc123';
            
            mockCAFS.storeContent.mockResolvedValue({
                success: true,
                contentHash,
                deduplicated: false,
                storagePath: 'cafs/ab/abc123'
            });

            jest.spyOn(resourceManager as any, 'storeResourceMetadata').mockResolvedValue(undefined);
            jest.spyOn(resourceManager as any, 'updateJobStepResources').mockResolvedValue(undefined);

            const result = await resourceManager.createResource(
                content,
                resourceType,
                mockJobStep,
                mockJob
            );

            expect(result.content).toEqual(content);
            expect(result.contentHash).toBe(contentHash);
            expect(result.resourceId.resourceType).toBe(resourceType);
            expect(result.resourceId.jobStepId).toBe(mockJobStep.id);
            expect(result.resourceId.jobId).toBe(mockJob.id);
            expect(result.metadata.customProperties.deduplicated).toBe(false);
        });

        it('should handle deduplicated content', async () => {
            const content = { data: 'existing' };
            const resourceType: ResourceType = 'object';
            const contentHash = 'def456';
            
            mockCAFS.storeContent.mockResolvedValue({
                success: true,
                contentHash,
                deduplicated: true,
                storagePath: 'cafs/de/def456'
            });

            jest.spyOn(resourceManager as any, 'storeResourceMetadata').mockResolvedValue(undefined);
            jest.spyOn(resourceManager as any, 'updateJobStepResources').mockResolvedValue(undefined);

            const result = await resourceManager.createResource(
                content,
                resourceType,
                mockJobStep,
                mockJob
            );

            expect(result.metadata.customProperties.deduplicated).toBe(true);
        });

        it('should throw error if CAFS storage fails', async () => {
            const content = { data: 'test' };
            const resourceType: ResourceType = 'object';
            
            mockCAFS.storeContent.mockResolvedValue({
                success: false,
                contentHash: '',
                deduplicated: false,
                storagePath: '',
                error: 'Storage failed'
            });

            await expect(resourceManager.createResource(
                content,
                resourceType,
                mockJobStep,
                mockJob
            )).rejects.toThrow('Storage failed');
        });
    });

    describe('createIntegerResource', () => {
        it('should create an integer resource', async () => {
            const value = 42;
            const contentHash = 'int123';
            
            mockCAFS.storeContent.mockResolvedValue({
                success: true,
                contentHash,
                deduplicated: false,
                storagePath: 'cafs/in/int123'
            });

            jest.spyOn(resourceManager as any, 'storeResourceMetadata').mockResolvedValue(undefined);
            jest.spyOn(resourceManager as any, 'updateJobStepResources').mockResolvedValue(undefined);

            const result = await resourceManager.createIntegerResource(
                value,
                mockJobStep,
                mockJob
            );

            expect(result.content).toEqual({ semanticIdentity: value });
            expect(result.resourceId.resourceType).toBe('integer');
            expect(result.metadata.tags).toContain('integer');
            expect(result.metadata.tags).toContain('semantic-identity');
            expect(result.metadata.customProperties.integerValue).toBe(value);
        });
    });

    describe('getResource', () => {
        it('should retrieve resource by ID', async () => {
            const resourceId = 'resource-123';
            const contentHash = 'abc123';
            const content = { data: 'test' };
            const serializedContent = JSON.stringify(content, null, 2);
            
            const resourceMetadata = {
                resourceId: {
                    id: resourceId,
                    resourceType: 'object' as ResourceType,
                    jobStepId: mockJobStep.id,
                    jobId: mockJob.id,
                    createdAt: new Date()
                },
                contentHash,
                metadata: {
                    contentSize: 100,
                    contentType: 'application/json',
                    createdAt: new Date(),
                    lastAccessedAt: new Date(),
                    referenceCount: 1,
                    tags: [],
                    customProperties: {}
                }
            };

            jest.spyOn(resourceManager as any, 'getResourceMetadata').mockResolvedValue(resourceMetadata);
            mockCAFS.retrieveContent.mockResolvedValue(serializedContent);
            jest.spyOn(resourceManager as any, 'storeResourceMetadata').mockResolvedValue(undefined);

            const result = await resourceManager.getResource(resourceId);

            expect(result).not.toBeNull();
            expect(result!.content).toEqual(content);
            expect(result!.contentHash).toBe(contentHash);
            expect(mockCAFS.retrieveContent).toHaveBeenCalledWith(contentHash);
        });

        it('should return null if resource not found', async () => {
            const resourceId = 'nonexistent';
            
            jest.spyOn(resourceManager as any, 'getResourceMetadata').mockResolvedValue(null);

            const result = await resourceManager.getResource(resourceId);

            expect(result).toBeNull();
        });

        it('should handle retrieval errors', async () => {
            const resourceId = 'resource-123';
            
            jest.spyOn(resourceManager as any, 'getResourceMetadata').mockRejectedValue(new Error('DB error'));

            await expect(resourceManager.getResource(resourceId))
                .rejects.toThrow('Failed to retrieve resource');
        });
    });

    describe('getResourceByHash', () => {
        it('should retrieve resource by content hash', async () => {
            const contentHash = 'abc123';
            const resourceMetadata = {
                resourceId: {
                    id: 'resource-123',
                    resourceType: 'object' as ResourceType,
                    jobStepId: mockJobStep.id,
                    jobId: mockJob.id,
                    createdAt: new Date()
                }
            };

            jest.spyOn(resourceManager as any, 'findResourceByHash').mockResolvedValue(resourceMetadata);
            jest.spyOn(resourceManager, 'getResource').mockResolvedValue({
                resourceId: resourceMetadata.resourceId,
                contentHash,
                content: { data: 'test' },
                metadata: {} as any
            });

            const result = await resourceManager.getResourceByHash(contentHash);

            expect(result).not.toBeNull();
            expect(result!.contentHash).toBe(contentHash);
        });

        it('should return null if no resource found with hash', async () => {
            const contentHash = 'nonexistent';
            
            jest.spyOn(resourceManager as any, 'findResourceByHash').mockResolvedValue(null);

            const result = await resourceManager.getResourceByHash(contentHash);

            expect(result).toBeNull();
        });
    });

    describe('findResourcesByContent', () => {
        it('should find resources with matching content', async () => {
            const content = { data: 'test' };
            const resourceType: ResourceType = 'object';
            const contentHash = 'abc123';
            
            jest.spyOn(resourceManager as any, 'serializeContent').mockReturnValue(JSON.stringify(content));
            mockCAFS['generateContentHash'] = jest.fn().mockReturnValue(contentHash);
            mockCAFS.contentExists.mockResolvedValue(true);
            
            const resourceMetadata = {
                resourceId: {
                    id: 'resource-123',
                    resourceType,
                    jobStepId: mockJobStep.id,
                    jobId: mockJob.id,
                    createdAt: new Date()
                }
            };

            jest.spyOn(resourceManager as any, 'findResourcesByHash').mockResolvedValue([resourceMetadata]);
            jest.spyOn(resourceManager, 'getResource').mockResolvedValue({
                resourceId: resourceMetadata.resourceId,
                contentHash,
                content,
                metadata: {} as any
            });

            const result = await resourceManager.findResourcesByContent(content, resourceType);

            expect(result).toHaveLength(1);
            expect(result[0].content).toEqual(content);
        });

        it('should return empty array if content not found', async () => {
            const content = { data: 'nonexistent' };
            const resourceType: ResourceType = 'object';
            
            jest.spyOn(resourceManager as any, 'serializeContent').mockReturnValue(JSON.stringify(content));
            mockCAFS['generateContentHash'] = jest.fn().mockReturnValue('nonexistent');
            mockCAFS.contentExists.mockResolvedValue(false);

            const result = await resourceManager.findResourcesByContent(content, resourceType);

            expect(result).toHaveLength(0);
        });
    });

    describe('deleteResource', () => {
        it('should delete resource successfully', async () => {
            const resourceId = 'resource-123';
            const contentHash = 'abc123';
            
            const resourceMetadata = {
                resourceId: {
                    id: resourceId,
                    resourceType: 'object' as ResourceType,
                    jobStepId: mockJobStep.id,
                    jobId: mockJob.id,
                    createdAt: new Date()
                },
                contentHash,
                metadata: {} as any
            };

            jest.spyOn(resourceManager as any, 'getResourceMetadata').mockResolvedValue(resourceMetadata);
            mockCAFS.deleteContent.mockResolvedValue(undefined);
            jest.spyOn(resourceManager as any, 'deleteResourceMetadata').mockResolvedValue(undefined);

            await resourceManager.deleteResource(resourceId);

            expect(mockCAFS.deleteContent).toHaveBeenCalledWith(contentHash, false);
        });

        it('should force delete when requested', async () => {
            const resourceId = 'resource-123';
            const contentHash = 'abc123';
            
            const resourceMetadata = {
                resourceId: {
                    id: resourceId,
                    resourceType: 'object' as ResourceType,
                    jobStepId: mockJobStep.id,
                    jobId: mockJob.id,
                    createdAt: new Date()
                },
                contentHash,
                metadata: {} as any
            };

            jest.spyOn(resourceManager as any, 'getResourceMetadata').mockResolvedValue(resourceMetadata);
            mockCAFS.deleteContent.mockResolvedValue(undefined);
            jest.spyOn(resourceManager as any, 'deleteResourceMetadata').mockResolvedValue(undefined);

            await resourceManager.deleteResource(resourceId, true);

            expect(mockCAFS.deleteContent).toHaveBeenCalledWith(contentHash, true);
        });

        it('should throw error if resource not found', async () => {
            const resourceId = 'nonexistent';
            
            jest.spyOn(resourceManager as any, 'getResourceMetadata').mockResolvedValue(null);

            await expect(resourceManager.deleteResource(resourceId))
                .rejects.toThrow('Resource nonexistent not found');
        });
    });

    describe('content serialization', () => {
        it('should serialize integer content correctly', () => {
            const content: IntegerInstance = { semanticIdentity: 42 };
            const result = (resourceManager as any).serializeContent(content, 'integer');
            expect(result).toBe(JSON.stringify(content, null, 2));
        });

        it('should serialize string content correctly', () => {
            const content = 'test string';
            const result = (resourceManager as any).serializeContent(content, 'string');
            expect(result).toBe(content);
        });

        it('should serialize object content correctly', () => {
            const content = { key: 'value' };
            const result = (resourceManager as any).serializeContent(content, 'object');
            expect(result).toBe(JSON.stringify(content, null, 2));
        });

        it('should serialize array content correctly', () => {
            const content = [1, 2, 3];
            const result = (resourceManager as any).serializeContent(content, 'array');
            expect(result).toBe(JSON.stringify(content, null, 2));
        });
    });

    describe('content deserialization', () => {
        it('should deserialize integer content correctly', () => {
            const serialized = JSON.stringify({ semanticIdentity: 42 }, null, 2);
            const result = (resourceManager as any).deserializeContent(serialized, 'integer');
            expect(result).toEqual({ semanticIdentity: 42 });
        });

        it('should deserialize string content correctly', () => {
            const serialized = 'test string';
            const result = (resourceManager as any).deserializeContent(serialized, 'string');
            expect(result).toBe(serialized);
        });

        it('should deserialize object content correctly', () => {
            const content = { key: 'value' };
            const serialized = JSON.stringify(content, null, 2);
            const result = (resourceManager as any).deserializeContent(serialized, 'object');
            expect(result).toEqual(content);
        });

        it('should deserialize array content correctly', () => {
            const content = [1, 2, 3];
            const serialized = JSON.stringify(content, null, 2);
            const result = (resourceManager as any).deserializeContent(serialized, 'array');
            expect(result).toEqual(content);
        });
    });
});
