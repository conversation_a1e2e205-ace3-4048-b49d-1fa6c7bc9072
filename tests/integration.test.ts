import { 
    GCSUtils, 
    CAFS, 
    ResourceManager, 
    FirestoreAdapter,
    createGCSUtils,
    createCAFS,
    createResourceManager,
    createFirestoreAdapter,
    readFromGCS,
    writeToGCS
} from '../src/index';

describe('Integration Tests', () => {
    describe('SDK Exports', () => {
        it('should export all main classes', () => {
            expect(GCSUtils).toBeDefined();
            expect(CAFS).toBeDefined();
            expect(ResourceManager).toBeDefined();
            expect(FirestoreAdapter).toBeDefined();
        });

        it('should export convenience functions', () => {
            expect(createGCSUtils).toBeDefined();
            expect(createCAFS).toBeDefined();
            expect(createResourceManager).toBeDefined();
            expect(createFirestoreAdapter).toBeDefined();
        });

        it('should export legacy functions', () => {
            expect(readFromGCS).toBeDefined();
            expect(writeToGCS).toBeDefined();
        });
    });

    describe('Convenience Functions', () => {
        it('should create GCSUtils instance', () => {
            const gcsUtils = createGCSUtils('test-bucket');
            expect(gcsUtils).toBeInstanceOf(GCSUtils);
        });

        it('should create CAFS instance', () => {
            const cafs = createCAFS({ bucketName: 'test-bucket' });
            expect(cafs).toBeInstanceOf(CAFS);
        });

        it('should create ResourceManager instance', () => {
            const resourceManager = createResourceManager({ bucketName: 'test-bucket' });
            expect(resourceManager).toBeInstanceOf(ResourceManager);
        });

        it('should create FirestoreAdapter instance', () => {
            const firestoreAdapter = createFirestoreAdapter('test-project');
            expect(firestoreAdapter).toBeInstanceOf(FirestoreAdapter);
        });

        it('should create FirestoreAdapter with custom collections', () => {
            const firestoreAdapter = createFirestoreAdapter('test-project', {
                resources: 'custom_resources',
                cafs: 'custom_cafs',
                jobs: 'custom_jobs',
                jobSteps: 'custom_job_steps'
            });
            expect(firestoreAdapter).toBeInstanceOf(FirestoreAdapter);
        });
    });

    describe('End-to-End CAFS Workflow', () => {
        let resourceManager: ResourceManager;
        let mockJobStep: any;
        let mockJob: any;

        beforeEach(() => {
            resourceManager = new ResourceManager({
                bucketName: 'test-bucket',
                enableDeduplication: true
            });

            mockJobStep = {
                id: 'step-123',
                jobId: 'job-456',
                name: 'Test Step',
                status: 'running',
                createdResources: []
            };

            mockJob = {
                id: 'job-456',
                name: 'Test Job',
                status: 'running',
                steps: [mockJobStep],
                createdAt: new Date()
            };
        });

        it('should handle complete resource lifecycle', async () => {
            // Mock the underlying CAFS operations
            const mockCAFS = (resourceManager as any).cafs;
            mockCAFS.storeContent = jest.fn().mockResolvedValue({
                success: true,
                contentHash: 'abc123',
                deduplicated: false,
                storagePath: 'cafs/ab/abc123'
            });
            mockCAFS.retrieveContent = jest.fn().mockResolvedValue(
                JSON.stringify({ semanticIdentity: 42 }, null, 2)
            );
            mockCAFS.deleteContent = jest.fn().mockResolvedValue(undefined);

            // Mock metadata operations
            jest.spyOn(resourceManager as any, 'storeResourceMetadata').mockResolvedValue(undefined);
            jest.spyOn(resourceManager as any, 'getResourceMetadata').mockResolvedValue({
                resourceId: {
                    id: 'resource-123',
                    resourceType: 'integer',
                    jobStepId: mockJobStep.id,
                    jobId: mockJob.id,
                    createdAt: new Date()
                },
                contentHash: 'abc123',
                metadata: {
                    contentSize: 100,
                    contentType: 'application/json',
                    createdAt: new Date(),
                    lastAccessedAt: new Date(),
                    referenceCount: 1,
                    tags: [],
                    customProperties: {}
                }
            });
            jest.spyOn(resourceManager as any, 'updateJobStepResources').mockResolvedValue(undefined);
            jest.spyOn(resourceManager as any, 'deleteResourceMetadata').mockResolvedValue(undefined);

            // 1. Create resource
            const resource = await resourceManager.createIntegerResource(42, mockJobStep, mockJob);
            expect(resource.content.semanticIdentity).toBe(42);
            expect(resource.resourceId.resourceType).toBe('integer');

            // 2. Retrieve resource
            const retrieved = await resourceManager.getResource(resource.resourceId.id);
            expect(retrieved).not.toBeNull();
            expect(retrieved!.content.semanticIdentity).toBe(42);

            // 3. Delete resource
            await resourceManager.deleteResource(resource.resourceId.id);
            expect(mockCAFS.deleteContent).toHaveBeenCalledWith('abc123', false);
        });

        it('should demonstrate deduplication', async () => {
            const mockCAFS = (resourceManager as any).cafs;
            
            // First call - new content
            mockCAFS.storeContent = jest.fn()
                .mockResolvedValueOnce({
                    success: true,
                    contentHash: 'abc123',
                    deduplicated: false,
                    storagePath: 'cafs/ab/abc123'
                })
                // Second call - deduplicated
                .mockResolvedValueOnce({
                    success: true,
                    contentHash: 'abc123',
                    deduplicated: true,
                    storagePath: 'cafs/ab/abc123'
                });

            jest.spyOn(resourceManager as any, 'storeResourceMetadata').mockResolvedValue(undefined);
            jest.spyOn(resourceManager as any, 'updateJobStepResources').mockResolvedValue(undefined);

            // Create first resource
            const resource1 = await resourceManager.createIntegerResource(42, mockJobStep, mockJob);
            expect(resource1.metadata.customProperties.deduplicated).toBe(false);

            // Create second resource with same content
            const resource2 = await resourceManager.createIntegerResource(42, mockJobStep, mockJob);
            expect(resource2.metadata.customProperties.deduplicated).toBe(true);

            // Both should have same content hash
            expect(resource1.contentHash).toBe(resource2.contentHash);
        });
    });

    describe('Legacy Function Compatibility', () => {
        it('should maintain backward compatibility with readFromGCS', async () => {
            // This would use the default GCSUtils instance
            // In a real test, we'd mock the underlying GCS operations
            expect(typeof readFromGCS).toBe('function');
        });

        it('should maintain backward compatibility with writeToGCS', async () => {
            // This would use the default GCSUtils instance
            // In a real test, we'd mock the underlying GCS operations
            expect(typeof writeToGCS).toBe('function');
        });
    });

    describe('Configuration Handling', () => {
        it('should use environment variables for default configuration', () => {
            process.env.BUCKET_NAME = 'env-bucket';
            
            const gcsUtils = new GCSUtils();
            // We can't directly test the private bucketName property,
            // but we can verify the instance was created successfully
            expect(gcsUtils).toBeInstanceOf(GCSUtils);
            
            delete process.env.BUCKET_NAME;
        });

        it('should override environment variables with explicit configuration', () => {
            process.env.BUCKET_NAME = 'env-bucket';
            
            const gcsUtils = new GCSUtils('explicit-bucket');
            expect(gcsUtils).toBeInstanceOf(GCSUtils);
            
            delete process.env.BUCKET_NAME;
        });
    });

    describe('Error Handling', () => {
        it('should handle CAFS storage failures gracefully', async () => {
            const resourceManager = new ResourceManager();
            const mockCAFS = (resourceManager as any).cafs;
            
            mockCAFS.storeContent = jest.fn().mockResolvedValue({
                success: false,
                contentHash: '',
                deduplicated: false,
                storagePath: '',
                error: 'Storage quota exceeded'
            });

            const mockJobStep = {
                id: 'step-123',
                jobId: 'job-456',
                name: 'Test Step',
                status: 'running',
                createdResources: []
            };

            const mockJob = {
                id: 'job-456',
                name: 'Test Job',
                status: 'running',
                steps: [mockJobStep],
                createdAt: new Date()
            };

            await expect(resourceManager.createIntegerResource(42, mockJobStep, mockJob))
                .rejects.toThrow('Storage quota exceeded');
        });

        it('should handle network errors gracefully', async () => {
            const gcsUtils = new GCSUtils('test-bucket');
            
            // Mock network error
            const mockStorage = (gcsUtils as any).storage;
            mockStorage.bucket = jest.fn().mockImplementation(() => {
                throw new Error('Network timeout');
            });

            await expect(gcsUtils.readFromGCS('test.json'))
                .rejects.toThrow('Failed to read file');
        });
    });

    describe('Type Safety', () => {
        it('should maintain type safety for different resource types', async () => {
            const resourceManager = new ResourceManager();
            
            // Mock successful operations
            const mockCAFS = (resourceManager as any).cafs;
            mockCAFS.storeContent = jest.fn().mockResolvedValue({
                success: true,
                contentHash: 'abc123',
                deduplicated: false,
                storagePath: 'cafs/ab/abc123'
            });
            
            jest.spyOn(resourceManager as any, 'storeResourceMetadata').mockResolvedValue(undefined);
            jest.spyOn(resourceManager as any, 'updateJobStepResources').mockResolvedValue(undefined);

            const mockJobStep = {
                id: 'step-123',
                jobId: 'job-456',
                name: 'Test Step',
                status: 'running',
                createdResources: []
            };

            const mockJob = {
                id: 'job-456',
                name: 'Test Job',
                status: 'running',
                steps: [mockJobStep],
                createdAt: new Date()
            };

            // Test different resource types
            const stringResource = await resourceManager.createResource(
                'test string',
                'string',
                mockJobStep,
                mockJob
            );
            expect(stringResource.resourceId.resourceType).toBe('string');

            const objectResource = await resourceManager.createResource(
                { key: 'value' },
                'object',
                mockJobStep,
                mockJob
            );
            expect(objectResource.resourceId.resourceType).toBe('object');

            const arrayResource = await resourceManager.createResource(
                [1, 2, 3],
                'array',
                mockJobStep,
                mockJob
            );
            expect(arrayResource.resourceId.resourceType).toBe('array');
        });
    });
});
