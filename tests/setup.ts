/**
 * Test setup and configuration
 */

// Mock Google Cloud Storage
jest.mock('@google-cloud/storage', () => {
    const mockFile = {
        exists: jest.fn(),
        download: jest.fn(),
        save: jest.fn(),
        delete: jest.fn(),
        getMetadata: jest.fn()
    };

    const mockBucket = {
        file: jest.fn(() => mockFile),
        getFiles: jest.fn()
    };

    const mockStorage = {
        bucket: jest.fn(() => mockBucket)
    };

    return {
        Storage: jest.fn(() => mockStorage)
    };
});

// Mock Google Cloud Firestore
jest.mock('@google-cloud/firestore', () => {
    const mockDoc = {
        get: jest.fn(),
        set: jest.fn(),
        update: jest.fn(),
        delete: jest.fn(),
        exists: false,
        data: jest.fn()
    };

    const mockCollection = {
        doc: jest.fn(() => mockDoc),
        where: jest.fn(() => ({
            get: jest.fn()
        })),
        get: jest.fn()
    };

    const mockFirestore = {
        collection: jest.fn(() => mockCollection),
        FieldValue: {
            arrayUnion: jest.fn()
        }
    };

    return {
        Firestore: jest.fn(() => mockFirestore)
    };
});

// Set up environment variables for testing
process.env.BUCKET_NAME = 'test-bucket';
process.env.NODE_ENV = 'test';

// Global test timeout
jest.setTimeout(30000);
