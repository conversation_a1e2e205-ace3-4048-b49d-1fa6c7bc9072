import { GCSUtils } from '../src/gcs-utils';

// Mock @google-cloud/storage
jest.mock('@google-cloud/storage', () => {
    const mockFile = {
        exists: jest.fn(),
        download: jest.fn(),
        save: jest.fn(),
        delete: jest.fn(),
        getMetadata: jest.fn()
    };

    const mockBucket = {
        file: jest.fn(() => mockFile),
        getFiles: jest.fn()
    };

    const mockStorage = jest.fn(() => ({
        bucket: jest.fn(() => mockBucket)
    }));

    return {
        Storage: mockStorage
    };
});

describe('GCSUtils', () => {
    let gcsUtils: GCSUtils;
    let mockStorage: any;
    let mockBucket: any;
    let mockFile: any;

    beforeEach(() => {
        jest.clearAllMocks();

        // Get the mocked instances
        const { Storage } = require('@google-cloud/storage');
        mockStorage = new Storage();
        mockBucket = mockStorage.bucket();
        mockFile = mockBucket.file();

        gcsUtils = new GCSUtils('test-bucket');
    });

    describe('readFromGCS', () => {
        it('should read and parse integer value from GCS', async () => {
            const testData = { semanticIdentity: 42 };
            const testContent = JSON.stringify(testData);
            
            mockFile.exists.mockResolvedValue([true]);
            mockFile.download.mockResolvedValue([Buffer.from(testContent)]);

            const result = await gcsUtils.readFromGCS('test-file.json');

            expect(result).toBe(42);
            expect(mockFile.exists).toHaveBeenCalled();
            expect(mockFile.download).toHaveBeenCalled();
        });

        it('should throw error if file does not exist', async () => {
            mockFile.exists.mockResolvedValue([false]);

            await expect(gcsUtils.readFromGCS('nonexistent.json'))
                .rejects.toThrow('File nonexistent.json does not exist');
        });

        it('should throw error if file does not contain valid number', async () => {
            const testData = { semanticIdentity: 'not-a-number' };
            const testContent = JSON.stringify(testData);
            
            mockFile.exists.mockResolvedValue([true]);
            mockFile.download.mockResolvedValue([Buffer.from(testContent)]);

            await expect(gcsUtils.readFromGCS('invalid.json'))
                .rejects.toThrow('does not contain a valid number value');
        });

        it('should validate content hash when requested', async () => {
            const testData = { semanticIdentity: 42 };
            const testContent = JSON.stringify(testData);
            const contentHash = gcsUtils.generateContentHash(testContent);
            
            mockFile.exists.mockResolvedValue([true]);
            mockFile.download.mockResolvedValue([Buffer.from(testContent)]);
            mockFile.getMetadata.mockResolvedValue([{
                metadata: { contentHash }
            }]);

            const result = await gcsUtils.readFromGCS('test-file.json', { validateHash: true });

            expect(result).toBe(42);
            expect(mockFile.getMetadata).toHaveBeenCalled();
        });
    });

    describe('writeToGCS', () => {
        it('should write integer value to GCS', async () => {
            mockFile.exists.mockResolvedValue([false]);
            mockFile.save.mockResolvedValue(undefined);

            await gcsUtils.writeToGCS('test-file.json', 42);

            expect(mockFile.save).toHaveBeenCalledWith(
                JSON.stringify({ semanticIdentity: 42 }, null, 2),
                expect.objectContaining({
                    contentType: 'application/json',
                    metadata: expect.objectContaining({
                        contentHash: expect.any(String),
                        createdAt: expect.any(String)
                    })
                })
            );
        });

        it('should throw error if file exists and overwrite is not allowed', async () => {
            mockFile.exists.mockResolvedValue([true]);

            await expect(gcsUtils.writeToGCS('existing.json', 42))
                .rejects.toThrow('already exists and overwrite is not allowed');
        });

        it('should overwrite file when overwrite option is true', async () => {
            mockFile.exists.mockResolvedValue([true]);
            mockFile.save.mockResolvedValue(undefined);

            await gcsUtils.writeToGCS('existing.json', 42, { overwrite: true });

            expect(mockFile.save).toHaveBeenCalled();
        });

        it('should include tags in metadata when provided', async () => {
            mockFile.exists.mockResolvedValue([false]);
            mockFile.save.mockResolvedValue(undefined);

            await gcsUtils.writeToGCS('test-file.json', 42, { tags: ['test', 'integer'] });

            expect(mockFile.save).toHaveBeenCalledWith(
                expect.any(String),
                expect.objectContaining({
                    metadata: expect.objectContaining({
                        tags: 'test,integer'
                    })
                })
            );
        });
    });

    describe('readRawContent', () => {
        it('should read raw content from GCS', async () => {
            const testContent = 'Hello, World!';
            
            mockFile.exists.mockResolvedValue([true]);
            mockFile.download.mockResolvedValue([Buffer.from(testContent)]);

            const result = await gcsUtils.readRawContent('test.txt');

            expect(result).toBe(testContent);
        });

        it('should throw error if file does not exist', async () => {
            mockFile.exists.mockResolvedValue([false]);

            await expect(gcsUtils.readRawContent('nonexistent.txt'))
                .rejects.toThrow('does not exist');
        });
    });

    describe('writeRawContent', () => {
        it('should write raw content to GCS', async () => {
            mockFile.save.mockResolvedValue(undefined);

            await gcsUtils.writeRawContent('test.txt', 'Hello, World!', 'text/plain');

            expect(mockFile.save).toHaveBeenCalledWith(
                'Hello, World!',
                expect.objectContaining({
                    metadata: expect.objectContaining({
                        contentType: 'text/plain',
                        metadata: expect.objectContaining({
                            contentHash: expect.any(String),
                            createdAt: expect.any(String)
                        })
                    })
                })
            );
        });
    });

    describe('generateContentHash', () => {
        it('should generate consistent SHA-256 hash', () => {
            const content = 'test content';
            const hash1 = gcsUtils.generateContentHash(content);
            const hash2 = gcsUtils.generateContentHash(content);

            expect(hash1).toBe(hash2);
            expect(hash1).toMatch(/^[a-f0-9]{64}$/);
        });

        it('should generate different hashes for different content', () => {
            const hash1 = gcsUtils.generateContentHash('content1');
            const hash2 = gcsUtils.generateContentHash('content2');

            expect(hash1).not.toBe(hash2);
        });
    });

    describe('fileExists', () => {
        it('should return true if file exists', async () => {
            mockFile.exists.mockResolvedValue([true]);

            const result = await gcsUtils.fileExists('existing.txt');

            expect(result).toBe(true);
        });

        it('should return false if file does not exist', async () => {
            mockFile.exists.mockResolvedValue([false]);

            const result = await gcsUtils.fileExists('nonexistent.txt');

            expect(result).toBe(false);
        });

        it('should return false on error', async () => {
            mockFile.exists.mockRejectedValue(new Error('Network error'));

            const result = await gcsUtils.fileExists('error.txt');

            expect(result).toBe(false);
        });
    });

    describe('deleteFile', () => {
        it('should delete file from GCS', async () => {
            mockFile.delete.mockResolvedValue(undefined);

            await gcsUtils.deleteFile('test.txt');

            expect(mockFile.delete).toHaveBeenCalled();
        });

        it('should throw error on delete failure', async () => {
            mockFile.delete.mockRejectedValue(new Error('Delete failed'));

            await expect(gcsUtils.deleteFile('test.txt'))
                .rejects.toThrow('Failed to delete file');
        });
    });

    describe('getFileMetadata', () => {
        it('should get file metadata from GCS', async () => {
            const metadata = { size: 1024, contentType: 'text/plain' };
            mockFile.getMetadata.mockResolvedValue([metadata]);

            const result = await gcsUtils.getFileMetadata('test.txt');

            expect(result).toEqual(metadata);
        });
    });

    describe('listFiles', () => {
        it('should list files in bucket', async () => {
            const files = [{ name: 'file1.txt' }, { name: 'file2.txt' }];
            mockBucket.getFiles = jest.fn().mockResolvedValue([files]);

            const result = await gcsUtils.listFiles();

            expect(result).toEqual(['file1.txt', 'file2.txt']);
        });

        it('should list files with prefix', async () => {
            const files = [{ name: 'prefix/file1.txt' }];
            mockBucket.getFiles = jest.fn().mockResolvedValue([files]);

            const result = await gcsUtils.listFiles('prefix/');

            expect(result).toEqual(['prefix/file1.txt']);
            expect(mockBucket.getFiles).toHaveBeenCalledWith({ prefix: 'prefix/' });
        });
    });
});
