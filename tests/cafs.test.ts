import { CAFS } from '../src/cafs';
import { GCSUtils } from '../src/gcs-utils';

// Mock GCSUtils
jest.mock('../src/gcs-utils');
const MockedGCSUtils = GCSUtils as jest.MockedClass<typeof GCSUtils>;

describe('CAFS', () => {
    let cafs: CAFS;
    let mockGCSUtils: jest.Mocked<GCSUtils>;

    beforeEach(() => {
        jest.clearAllMocks();
        
        // Create mock instance
        mockGCSUtils = {
            fileExists: jest.fn(),
            readRawContent: jest.fn(),
            writeRawContent: jest.fn(),
            deleteFile: jest.fn(),
            listFiles: jest.fn(),
            generateContentHash: jest.fn()
        } as any;

        MockedGCSUtils.mockImplementation(() => mockGCSUtils);
        
        cafs = new CAFS({
            bucketName: 'test-bucket',
            enableDeduplication: true,
            maxFileSize: 1024 * 1024 // 1MB
        });
    });

    describe('storeContent', () => {
        it('should store new content successfully', async () => {
            const content = 'test content';
            const contentHash = 'abc123';
            
            mockGCSUtils.fileExists.mockResolvedValue(false);
            mockGCSUtils.writeRawContent.mockResolvedValue(undefined);
            
            // Mock the private generateContentHash method
            jest.spyOn(cafs as any, 'generateContentHash').mockReturnValue(contentHash);
            jest.spyOn(cafs as any, 'storeCAFSMetadata').mockResolvedValue(undefined);

            const result = await cafs.storeContent(content);

            expect(result.success).toBe(true);
            expect(result.contentHash).toBe(contentHash);
            expect(result.deduplicated).toBe(false);
            expect(mockGCSUtils.writeRawContent).toHaveBeenCalled();
        });

        it('should deduplicate existing content', async () => {
            const content = 'existing content';
            const contentHash = 'def456';
            
            mockGCSUtils.fileExists.mockResolvedValue(true);
            
            jest.spyOn(cafs as any, 'generateContentHash').mockReturnValue(contentHash);
            jest.spyOn(cafs as any, 'updateReferenceCount').mockResolvedValue(undefined);

            const result = await cafs.storeContent(content);

            expect(result.success).toBe(true);
            expect(result.contentHash).toBe(contentHash);
            expect(result.deduplicated).toBe(true);
            expect(mockGCSUtils.writeRawContent).not.toHaveBeenCalled();
        });

        it('should reject content that exceeds max file size', async () => {
            const largeContent = 'x'.repeat(2 * 1024 * 1024); // 2MB

            const result = await cafs.storeContent(largeContent);

            expect(result.success).toBe(false);
            expect(result.error).toContain('exceeds maximum allowed size');
        });

        it('should handle storage errors gracefully', async () => {
            const content = 'test content';
            
            mockGCSUtils.fileExists.mockResolvedValue(false);
            mockGCSUtils.writeRawContent.mockRejectedValue(new Error('Storage error'));
            
            jest.spyOn(cafs as any, 'generateContentHash').mockReturnValue('abc123');

            const result = await cafs.storeContent(content);

            expect(result.success).toBe(false);
            expect(result.error).toContain('Failed to store content');
        });
    });

    describe('retrieveContent', () => {
        it('should retrieve content by hash', async () => {
            const contentHash = 'abc123';
            const content = 'test content';
            
            mockGCSUtils.fileExists.mockResolvedValue(true);
            mockGCSUtils.readRawContent.mockResolvedValue(content);
            
            jest.spyOn(cafs as any, 'generateContentHash').mockReturnValue(contentHash);
            jest.spyOn(cafs as any, 'updateLastAccessTime').mockResolvedValue(undefined);

            const result = await cafs.retrieveContent(contentHash);

            expect(result).toBe(content);
            expect(mockGCSUtils.readRawContent).toHaveBeenCalled();
        });

        it('should throw error if content does not exist', async () => {
            const contentHash = 'nonexistent';
            
            mockGCSUtils.fileExists.mockResolvedValue(false);

            await expect(cafs.retrieveContent(contentHash))
                .rejects.toThrow('Content with hash nonexistent not found');
        });

        it('should verify content hash integrity', async () => {
            const contentHash = 'abc123';
            const content = 'test content';
            const wrongHash = 'def456';
            
            mockGCSUtils.fileExists.mockResolvedValue(true);
            mockGCSUtils.readRawContent.mockResolvedValue(content);
            
            jest.spyOn(cafs as any, 'generateContentHash').mockReturnValue(wrongHash);

            await expect(cafs.retrieveContent(contentHash))
                .rejects.toThrow('Content hash mismatch');
        });

        it('should not update access time when requested', async () => {
            const contentHash = 'abc123';
            const content = 'test content';
            
            mockGCSUtils.fileExists.mockResolvedValue(true);
            mockGCSUtils.readRawContent.mockResolvedValue(content);
            
            jest.spyOn(cafs as any, 'generateContentHash').mockReturnValue(contentHash);
            const updateAccessTimeSpy = jest.spyOn(cafs as any, 'updateLastAccessTime').mockResolvedValue(undefined);

            await cafs.retrieveContent(contentHash, false);

            expect(updateAccessTimeSpy).not.toHaveBeenCalled();
        });
    });

    describe('contentExists', () => {
        it('should return true if content exists', async () => {
            const contentHash = 'abc123';
            
            mockGCSUtils.fileExists.mockResolvedValue(true);

            const result = await cafs.contentExists(contentHash);

            expect(result).toBe(true);
        });

        it('should return false if content does not exist', async () => {
            const contentHash = 'nonexistent';
            
            mockGCSUtils.fileExists.mockResolvedValue(false);

            const result = await cafs.contentExists(contentHash);

            expect(result).toBe(false);
        });
    });

    describe('deleteContent', () => {
        it('should delete content when reference count reaches zero', async () => {
            const contentHash = 'abc123';
            const cafsEntry = {
                contentHash,
                gcsPath: 'cafs/ab/abc123',
                metadata: { referenceCount: 1 } as any,
                referencedBy: []
            };
            
            jest.spyOn(cafs as any, 'getCAFSMetadata').mockResolvedValue(cafsEntry);
            jest.spyOn(cafs as any, 'updateReferenceCount').mockResolvedValue(undefined);
            mockGCSUtils.deleteFile.mockResolvedValue(undefined);
            jest.spyOn(cafs as any, 'deleteCAFSMetadata').mockResolvedValue(undefined);

            await cafs.deleteContent(contentHash);

            expect(mockGCSUtils.deleteFile).toHaveBeenCalledWith(cafsEntry.gcsPath);
        });

        it('should not delete content when reference count is greater than zero', async () => {
            const contentHash = 'abc123';
            const cafsEntry = {
                contentHash,
                gcsPath: 'cafs/ab/abc123',
                metadata: { referenceCount: 2 } as any,
                referencedBy: []
            };
            
            jest.spyOn(cafs as any, 'getCAFSMetadata').mockResolvedValue(cafsEntry);
            jest.spyOn(cafs as any, 'updateReferenceCount').mockResolvedValue(undefined);

            await cafs.deleteContent(contentHash);

            expect(mockGCSUtils.deleteFile).not.toHaveBeenCalled();
        });

        it('should force delete when requested', async () => {
            const contentHash = 'abc123';
            const cafsEntry = {
                contentHash,
                gcsPath: 'cafs/ab/abc123',
                metadata: { referenceCount: 5 } as any,
                referencedBy: []
            };
            
            jest.spyOn(cafs as any, 'getCAFSMetadata').mockResolvedValue(cafsEntry);
            mockGCSUtils.deleteFile.mockResolvedValue(undefined);
            jest.spyOn(cafs as any, 'deleteCAFSMetadata').mockResolvedValue(undefined);

            await cafs.deleteContent(contentHash, true);

            expect(mockGCSUtils.deleteFile).toHaveBeenCalledWith(cafsEntry.gcsPath);
        });

        it('should throw error if CAFS entry not found', async () => {
            const contentHash = 'nonexistent';
            
            jest.spyOn(cafs as any, 'getCAFSMetadata').mockResolvedValue(null);

            await expect(cafs.deleteContent(contentHash))
                .rejects.toThrow('CAFS entry not found');
        });
    });

    describe('getCAFSEntry', () => {
        it('should return CAFS entry if exists', async () => {
            const contentHash = 'abc123';
            const cafsEntry = {
                contentHash,
                gcsPath: 'cafs/ab/abc123',
                metadata: {} as any,
                referencedBy: []
            };
            
            jest.spyOn(cafs as any, 'getCAFSMetadata').mockResolvedValue(cafsEntry);

            const result = await cafs.getCAFSEntry(contentHash);

            expect(result).toEqual(cafsEntry);
        });

        it('should return null if CAFS entry does not exist', async () => {
            const contentHash = 'nonexistent';
            
            jest.spyOn(cafs as any, 'getCAFSMetadata').mockResolvedValue(null);

            const result = await cafs.getCAFSEntry(contentHash);

            expect(result).toBeNull();
        });
    });

    describe('listCAFSEntries', () => {
        it('should list all CAFS entries', async () => {
            const files = ['cafs/ab/abc123', 'cafs/de/def456', 'cafs/metadata/abc123.json'];
            const cafsEntry1 = { contentHash: 'abc123' } as any;
            const cafsEntry2 = { contentHash: 'def456' } as any;
            
            mockGCSUtils.listFiles.mockResolvedValue(files);
            jest.spyOn(cafs as any, 'extractHashFromPath')
                .mockReturnValueOnce('abc123')
                .mockReturnValueOnce('def456')
                .mockReturnValueOnce(null);
            jest.spyOn(cafs as any, 'getCAFSMetadata')
                .mockResolvedValueOnce(cafsEntry1)
                .mockResolvedValueOnce(cafsEntry2);

            const result = await cafs.listCAFSEntries();

            expect(result).toEqual([cafsEntry1, cafsEntry2]);
        });

        it('should filter CAFS entries when filter function provided', async () => {
            const files = ['cafs/ab/abc123', 'cafs/de/def456'];
            const cafsEntry1 = { contentHash: 'abc123', metadata: { tags: ['test'] } } as any;
            const cafsEntry2 = { contentHash: 'def456', metadata: { tags: ['prod'] } } as any;
            
            mockGCSUtils.listFiles.mockResolvedValue(files);
            jest.spyOn(cafs as any, 'extractHashFromPath')
                .mockReturnValueOnce('abc123')
                .mockReturnValueOnce('def456');
            jest.spyOn(cafs as any, 'getCAFSMetadata')
                .mockResolvedValueOnce(cafsEntry1)
                .mockResolvedValueOnce(cafsEntry2);

            const filter = (entry: any) => entry.metadata.tags.includes('test');
            const result = await cafs.listCAFSEntries(filter);

            expect(result).toEqual([cafsEntry1]);
        });
    });
});
