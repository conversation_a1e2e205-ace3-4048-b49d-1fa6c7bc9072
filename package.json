{"name": "gcs-utils-sdk", "version": "1.0.0", "description": "TypeScript SDK for Google Cloud Storage with Content Addressable File Storage (CAFS) support", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "clean": "<PERSON><PERSON><PERSON> dist"}, "keywords": ["google-cloud-storage", "gcs", "content-addressable", "cafs", "typescript", "sdk"], "author": "Your Name", "license": "MIT", "dependencies": {"@google-cloud/storage": "^7.7.0", "@google-cloud/firestore": "^7.1.0", "uuid": "^9.0.1"}, "devDependencies": {"@types/node": "^20.10.0", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0", "typescript": "^5.3.2", "rimraf": "^5.0.5"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/your-username/gcs-utils-sdk.git"}, "bugs": {"url": "https://github.com/your-username/gcs-utils-sdk/issues"}, "homepage": "https://github.com/your-username/gcs-utils-sdk#readme"}