/**
 * Basic usage examples for GCS Utils SDK
 */

import { 
    GCSUtils, 
    CAFS, 
    ResourceManager,
    createGCSUtils,
    createCAFS,
    createResourceManager
} from '../src/index';

// Example 1: Basic GCS operations
async function basicGCSOperations() {
    console.log('=== Basic GCS Operations ===');
    
    const gcsUtils = createGCSUtils('my-test-bucket');
    
    try {
        // Write an integer value
        await gcsUtils.writeToGCS('numbers/answer.json', 42);
        console.log('✓ Written value 42 to numbers/answer.json');
        
        // Read the value back
        const value = await gcsUtils.readFromGCS('numbers/answer.json');
        console.log(`✓ Read value: ${value}`);
        
        // Check if file exists
        const exists = await gcsUtils.fileExists('numbers/answer.json');
        console.log(`✓ File exists: ${exists}`);
        
        // List files
        const files = await gcsUtils.listFiles('numbers/');
        console.log(`✓ Found ${files.length} files in numbers/ directory`);
        
    } catch (error) {
        console.error('Error in basic GCS operations:', error);
    }
}

// Example 2: CAFS operations with deduplication
async function cafsOperations() {
    console.log('\n=== CAFS Operations ===');
    
    const cafs = createCAFS({
        bucketName: 'my-test-bucket',
        enableDeduplication: true,
        maxFileSize: 1024 * 1024 // 1MB
    });
    
    try {
        // Store content for the first time
        const result1 = await cafs.storeContent('Hello, CAFS World!', {
            tags: ['example', 'greeting'],
            customProperties: { language: 'en' }
        });
        
        console.log(`✓ Stored content with hash: ${result1.contentHash}`);
        console.log(`✓ Deduplicated: ${result1.deduplicated}`);
        
        // Store the same content again (should be deduplicated)
        const result2 = await cafs.storeContent('Hello, CAFS World!');
        console.log(`✓ Second storage - Deduplicated: ${result2.deduplicated}`);
        console.log(`✓ Same hash: ${result1.contentHash === result2.contentHash}`);
        
        // Retrieve content by hash
        const retrieved = await cafs.retrieveContent(result1.contentHash);
        console.log(`✓ Retrieved content: "${retrieved}"`);
        
        // Check if content exists
        const exists = await cafs.contentExists(result1.contentHash);
        console.log(`✓ Content exists: ${exists}`);
        
        // Get CAFS entry metadata
        const entry = await cafs.getCAFSEntry(result1.contentHash);
        if (entry) {
            console.log(`✓ Reference count: ${entry.metadata.referenceCount}`);
            console.log(`✓ Content size: ${entry.metadata.contentSize} bytes`);
        }
        
    } catch (error) {
        console.error('Error in CAFS operations:', error);
    }
}

// Example 3: Resource management
async function resourceManagement() {
    console.log('\n=== Resource Management ===');
    
    const resourceManager = createResourceManager({
        bucketName: 'my-test-bucket',
        enableDeduplication: true
    });
    
    // Create mock job and job step
    const job = {
        id: 'example-job-001',
        name: 'Example Data Processing Job',
        status: 'running' as const,
        steps: [],
        createdAt: new Date()
    };
    
    const jobStep = {
        id: 'example-step-001',
        jobId: job.id,
        name: 'Process Integer Values',
        status: 'running' as const,
        createdResources: []
    };
    
    try {
        // Create integer resources
        const resource1 = await resourceManager.createIntegerResource(42, jobStep, job);
        console.log(`✓ Created integer resource: ${resource1.resourceId.id}`);
        console.log(`✓ Content hash: ${resource1.contentHash}`);
        
        const resource2 = await resourceManager.createIntegerResource(42, jobStep, job);
        console.log(`✓ Created second integer resource: ${resource2.resourceId.id}`);
        console.log(`✓ Same content hash: ${resource1.contentHash === resource2.contentHash}`);
        
        // Create different types of resources
        const stringResource = await resourceManager.createResource(
            'Hello, Resource World!',
            'string',
            jobStep,
            job,
            { tags: ['string', 'greeting'] }
        );
        console.log(`✓ Created string resource: ${stringResource.resourceId.id}`);
        
        const objectResource = await resourceManager.createResource(
            { name: 'Example', value: 123, active: true },
            'object',
            jobStep,
            job,
            { tags: ['object', 'config'] }
        );
        console.log(`✓ Created object resource: ${objectResource.resourceId.id}`);
        
        const arrayResource = await resourceManager.createResource(
            [1, 2, 3, 4, 5],
            'array',
            jobStep,
            job,
            { tags: ['array', 'numbers'] }
        );
        console.log(`✓ Created array resource: ${arrayResource.resourceId.id}`);
        
        // Retrieve resources
        const retrieved = await resourceManager.getResource(resource1.resourceId.id);
        if (retrieved) {
            console.log(`✓ Retrieved resource value: ${retrieved.content.semanticIdentity}`);
        }
        
        // Find resources by content
        const duplicates = await resourceManager.findResourcesByContent(
            { semanticIdentity: 42 },
            'integer'
        );
        console.log(`✓ Found ${duplicates.length} resources with value 42`);
        
        // Get resource by hash
        const byHash = await resourceManager.getResourceByHash(resource1.contentHash);
        if (byHash) {
            console.log(`✓ Retrieved by hash: ${byHash.content.semanticIdentity}`);
        }
        
    } catch (error) {
        console.error('Error in resource management:', error);
    }
}

// Example 4: Advanced CAFS features
async function advancedCAFSFeatures() {
    console.log('\n=== Advanced CAFS Features ===');
    
    const cafs = createCAFS({
        bucketName: 'my-test-bucket',
        enableDeduplication: true
    });
    
    try {
        // Store multiple different contents
        const contents = [
            'First document content',
            'Second document content',
            'Third document content',
            'First document content' // Duplicate
        ];
        
        const results = [];
        for (let i = 0; i < contents.length; i++) {
            const result = await cafs.storeContent(contents[i], {
                tags: [`doc-${i + 1}`],
                customProperties: { index: i }
            });
            results.push(result);
            console.log(`✓ Document ${i + 1}: ${result.deduplicated ? 'deduplicated' : 'new'}`);
        }
        
        // List all CAFS entries
        const entries = await cafs.listCAFSEntries();
        console.log(`✓ Total unique contents in CAFS: ${entries.length}`);
        
        // Filter entries by criteria
        const recentEntries = await cafs.listCAFSEntries(entry => {
            const ageInMinutes = (Date.now() - entry.metadata.createdAt.getTime()) / (1000 * 60);
            return ageInMinutes < 60; // Created in last hour
        });
        console.log(`✓ Recent entries: ${recentEntries.length}`);
        
        // Demonstrate reference counting
        for (const entry of entries) {
            console.log(`✓ Hash ${entry.contentHash.substring(0, 8)}... has ${entry.metadata.referenceCount} references`);
        }
        
    } catch (error) {
        console.error('Error in advanced CAFS features:', error);
    }
}

// Example 5: Error handling and edge cases
async function errorHandlingExamples() {
    console.log('\n=== Error Handling Examples ===');
    
    const gcsUtils = createGCSUtils('my-test-bucket');
    const cafs = createCAFS({ bucketName: 'my-test-bucket' });
    
    try {
        // Try to read non-existent file
        try {
            await gcsUtils.readFromGCS('non-existent.json');
        } catch (error) {
            console.log('✓ Correctly caught error for non-existent file');
        }
        
        // Try to retrieve non-existent content from CAFS
        try {
            await cafs.retrieveContent('nonexistenthash123456789');
        } catch (error) {
            console.log('✓ Correctly caught error for non-existent content hash');
        }
        
        // Try to store content that's too large
        const largeContent = 'x'.repeat(100 * 1024 * 1024); // 100MB
        const result = await cafs.storeContent(largeContent);
        if (!result.success) {
            console.log('✓ Correctly rejected oversized content');
        }
        
        // Check content existence
        const exists = await cafs.contentExists('nonexistenthash');
        console.log(`✓ Non-existent content check: ${exists}`);
        
    } catch (error) {
        console.error('Unexpected error in error handling examples:', error);
    }
}

// Run all examples
async function runAllExamples() {
    console.log('GCS Utils SDK - Usage Examples\n');
    
    await basicGCSOperations();
    await cafsOperations();
    await resourceManagement();
    await advancedCAFSFeatures();
    await errorHandlingExamples();
    
    console.log('\n=== All Examples Completed ===');
}

// Export for use in other files
export {
    basicGCSOperations,
    cafsOperations,
    resourceManagement,
    advancedCAFSFeatures,
    errorHandlingExamples,
    runAllExamples
};

// Run examples if this file is executed directly
if (require.main === module) {
    runAllExamples().catch(console.error);
}
