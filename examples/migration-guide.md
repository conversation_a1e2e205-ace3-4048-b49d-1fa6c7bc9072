# Migration Guide: From Legacy to CAFS

This guide helps you migrate from the legacy file-based storage system to the new Content Addressable File Storage (CAFS) system.

## Overview

The legacy system stored resources with predictable file names (e.g., `0.json`, `1.json`, `2.json`) in Google Cloud Storage. The new CAFS system stores resources using their content hash as the file name, enabling automatic deduplication and more efficient storage.

## Key Differences

### Legacy System
- Files named by content: `0.json`, `1.json`, `2.json`
- No deduplication (duplicate content stored multiple times)
- Simple metadata in Firestore
- Direct file path to content mapping

### CAFS System
- Files named by SHA-256 hash: `cafs/ab/abc123...`
- Automatic deduplication (identical content stored once)
- Rich metadata tracking in Firestore
- Content-addressable storage with reference counting

## Migration Steps

### Step 1: Install the New SDK

```bash
npm install gcs-utils-sdk
```

### Step 2: Update Imports

**Before:**
```typescript
// Legacy direct functions
import { readFromGCS, writeToGCS } from './old-gcs-utils';
```

**After:**
```typescript
// New SDK with backward compatibility
import { GCSUtils, ResourceManager, readFromGCS, writeToGCS } from 'gcs-utils-sdk';
```

### Step 3: Gradual Migration Approach

#### Option A: Immediate Migration (Recommended)

Replace legacy calls with ResourceManager:

**Before:**
```typescript
// Legacy approach
await writeToGCS('42.json', 42);
const value = await readFromGCS('42.json');
```

**After:**
```typescript
// New CAFS approach
const resourceManager = new ResourceManager();

// Create resource with job context
const resource = await resourceManager.createIntegerResource(42, jobStep, job);
const retrieved = await resourceManager.getResource(resource.resourceId.id);
const value = retrieved.content.semanticIdentity;
```

#### Option B: Gradual Migration

Use legacy functions during transition:

```typescript
import { readFromGCS, writeToGCS, ResourceManager } from 'gcs-utils-sdk';

// Continue using legacy functions for existing code
const legacyValue = await readFromGCS('existing-file.json');

// Use new system for new features
const resourceManager = new ResourceManager();
const newResource = await resourceManager.createIntegerResource(42, jobStep, job);
```

### Step 4: Migrate Existing Data

Create a migration script to move existing data to CAFS:

```typescript
import { GCSUtils, ResourceManager } from 'gcs-utils-sdk';

async function migrateExistingData() {
    const gcsUtils = new GCSUtils('tp-resources');
    const resourceManager = new ResourceManager();
    
    // List all existing JSON files
    const files = await gcsUtils.listFiles();
    const jsonFiles = files.filter(f => f.endsWith('.json') && /^\d+\.json$/.test(f));
    
    for (const file of jsonFiles) {
        try {
            // Read legacy content
            const value = await gcsUtils.readFromGCS(file);
            
            // Create job context for migration
            const migrationJob = {
                id: 'migration-job',
                name: 'Legacy Data Migration',
                status: 'running' as const,
                steps: [],
                createdAt: new Date()
            };
            
            const migrationStep = {
                id: `migrate-${file}`,
                jobId: migrationJob.id,
                name: `Migrate ${file}`,
                status: 'running' as const,
                createdResources: []
            };
            
            // Create resource in CAFS
            const resource = await resourceManager.createIntegerResource(
                value, 
                migrationStep, 
                migrationJob
            );
            
            console.log(`Migrated ${file} -> ${resource.contentHash}`);
            
            // Optionally, keep a mapping for reference
            await gcsUtils.writeRawContent(
                `migration/mapping/${file}.txt`,
                JSON.stringify({
                    originalFile: file,
                    resourceId: resource.resourceId.id,
                    contentHash: resource.contentHash,
                    migratedAt: new Date().toISOString()
                }),
                'application/json'
            );
            
        } catch (error) {
            console.error(`Failed to migrate ${file}:`, error);
        }
    }
}
```

### Step 5: Update Job Integration

Integrate CAFS with your job processing system:

```typescript
import { ResourceManager } from 'gcs-utils-sdk';

class JobProcessor {
    private resourceManager: ResourceManager;
    
    constructor() {
        this.resourceManager = new ResourceManager({
            bucketName: 'tp-resources',
            enableDeduplication: true
        });
    }
    
    async processJobStep(jobStep: JobStep, job: Job, inputValue: number): Promise<void> {
        // Process the value (your business logic here)
        const result = inputValue * 2;
        
        // Store result as a resource
        const resource = await this.resourceManager.createIntegerResource(
            result,
            jobStep,
            job
        );
        
        // The resource is automatically deduplicated if the same result exists
        console.log(`Created resource ${resource.resourceId.id} with value ${result}`);
        console.log(`Deduplicated: ${resource.metadata.customProperties.deduplicated}`);
        
        // Update job step with created resource
        jobStep.createdResources.push(resource.resourceId.id);
    }
    
    async getJobResults(jobId: string): Promise<number[]> {
        // Get all resources created by this job
        const resources = await this.resourceManager.listResources(jobId);
        
        // Extract integer values
        const values: number[] = [];
        for (const resourceMeta of resources) {
            const resource = await this.resourceManager.getResource(resourceMeta.resourceId.id);
            if (resource && resource.resourceId.resourceType === 'integer') {
                values.push(resource.content.semanticIdentity);
            }
        }
        
        return values;
    }
}
```

## Benefits After Migration

### 1. Automatic Deduplication

```typescript
// Multiple job steps creating the same result
const resource1 = await resourceManager.createIntegerResource(42, step1, job);
const resource2 = await resourceManager.createIntegerResource(42, step2, job);

// Only one copy stored in GCS, but both resources reference it
console.log(resource1.contentHash === resource2.contentHash); // true
```

### 2. Content Integrity

```typescript
// Content is verified by hash
const content = await cafs.retrieveContent(contentHash);
// Automatically verifies that content matches the hash
```

### 3. Efficient Storage

```typescript
// Find all resources with the same content
const duplicates = await resourceManager.findResourcesByContent(
    { semanticIdentity: 42 },
    'integer'
);
console.log(`Found ${duplicates.length} resources sharing the same content`);
```

### 4. Rich Metadata

```typescript
// Resources include comprehensive metadata
const resource = await resourceManager.getResource(resourceId);
console.log('Created at:', resource.metadata.createdAt);
console.log('Last accessed:', resource.metadata.lastAccessedAt);
console.log('Reference count:', resource.metadata.referenceCount);
console.log('Tags:', resource.metadata.tags);
console.log('Job context:', resource.resourceId.jobId);
```

## Backward Compatibility

The SDK maintains backward compatibility:

```typescript
// Legacy functions still work
import { readFromGCS, writeToGCS } from 'gcs-utils-sdk';

await writeToGCS('legacy-file.json', 42);
const value = await readFromGCS('legacy-file.json');
```

## Best Practices

1. **Use ResourceManager for new code**: Provides full CAFS benefits
2. **Migrate gradually**: Start with new features, migrate existing code over time
3. **Leverage deduplication**: Design jobs to benefit from content sharing
4. **Use proper job context**: Provide meaningful job and job step information
5. **Monitor storage efficiency**: Track deduplication rates and storage savings

## Troubleshooting

### Common Issues

1. **Missing job context**: Ensure you provide valid Job and JobStep objects
2. **Large content**: Check maxFileSize configuration for large resources
3. **Hash mismatches**: Verify content integrity if hash validation fails
4. **Reference counting**: Understand that content is only deleted when reference count reaches zero

### Debug Tools

```typescript
// Check CAFS entry details
const entry = await cafs.getCAFSEntry(contentHash);
console.log('Reference count:', entry?.metadata.referenceCount);

// List all CAFS entries
const entries = await cafs.listCAFSEntries();
console.log('Total unique contents:', entries.length);

// Find resources by content
const resources = await resourceManager.findResourcesByContent(content, type);
console.log('Resources with this content:', resources.length);
```
