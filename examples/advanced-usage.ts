/**
 * Advanced usage examples for GCS Utils SDK
 * Demonstrates complex scenarios and integration patterns
 */

import { 
    ResourceManager, 
    CAFS, 
    FirestoreAdapter,
    Resource,
    Job,
    JobStep,
    ResourceType
} from '../src/index';

// Example 1: Custom Resource Types and Complex Data
interface DatasetResource {
    name: string;
    description: string;
    data: number[];
    metadata: {
        source: string;
        version: string;
        createdBy: string;
        tags: string[];
    };
}

interface ModelResource {
    modelType: 'linear' | 'neural' | 'tree';
    parameters: Record<string, any>;
    accuracy: number;
    trainingData: string; // Reference to dataset resource ID
}

async function advancedResourceTypes() {
    console.log('=== Advanced Resource Types ===');
    
    const resourceManager = new ResourceManager({
        bucketName: 'ml-pipeline-resources',
        enableDeduplication: true
    });
    
    // Create job context for ML pipeline
    const mlJob: Job = {
        id: 'ml-training-job-001',
        name: 'Customer Segmentation ML Pipeline',
        status: 'running',
        steps: [],
        createdAt: new Date()
    };
    
    const dataPreprocessingStep: JobStep = {
        id: 'data-preprocessing-step',
        jobId: mlJob.id,
        name: 'Data Preprocessing',
        status: 'running',
        createdResources: []
    };
    
    const modelTrainingStep: JobStep = {
        id: 'model-training-step',
        jobId: mlJob.id,
        name: 'Model Training',
        status: 'running',
        createdResources: []
    };
    
    try {
        // Create dataset resource
        const dataset: DatasetResource = {
            name: 'Customer Purchase Data',
            description: 'Historical customer purchase data for segmentation',
            data: [100, 250, 75, 300, 150, 200, 180, 220, 90, 350],
            metadata: {
                source: 'customer-database',
                version: '2024-01-15',
                createdBy: 'data-team',
                tags: ['customer', 'purchase', 'historical']
            }
        };
        
        const datasetResource = await resourceManager.createResource(
            dataset,
            'object',
            dataPreprocessingStep,
            mlJob,
            {
                tags: ['dataset', 'ml-input'],
                customProperties: {
                    dataType: 'customer-purchase',
                    recordCount: dataset.data.length
                }
            }
        );
        
        console.log(`✓ Created dataset resource: ${datasetResource.resourceId.id}`);
        
        // Create model resource that references the dataset
        const model: ModelResource = {
            modelType: 'neural',
            parameters: {
                layers: [10, 5, 3],
                activation: 'relu',
                learningRate: 0.001,
                epochs: 100
            },
            accuracy: 0.87,
            trainingData: datasetResource.resourceId.id
        };
        
        const modelResource = await resourceManager.createResource(
            model,
            'object',
            modelTrainingStep,
            mlJob,
            {
                tags: ['model', 'neural-network', 'trained'],
                customProperties: {
                    modelType: model.modelType,
                    accuracy: model.accuracy,
                    referencesDataset: datasetResource.resourceId.id
                }
            }
        );
        
        console.log(`✓ Created model resource: ${modelResource.resourceId.id}`);
        console.log(`✓ Model references dataset: ${model.trainingData}`);
        
        // Demonstrate resource relationships
        const retrievedModel = await resourceManager.getResource<ModelResource>(modelResource.resourceId.id);
        if (retrievedModel) {
            const referencedDataset = await resourceManager.getResource<DatasetResource>(
                retrievedModel.content.trainingData
            );
            if (referencedDataset) {
                console.log(`✓ Model trained on dataset: ${referencedDataset.content.name}`);
                console.log(`✓ Dataset size: ${referencedDataset.content.data.length} records`);
            }
        }
        
    } catch (error) {
        console.error('Error in advanced resource types:', error);
    }
}

// Example 2: Batch Operations and Performance Optimization
async function batchOperationsExample() {
    console.log('\n=== Batch Operations ===');
    
    const resourceManager = new ResourceManager({
        bucketName: 'batch-processing-bucket',
        enableDeduplication: true
    });
    
    const batchJob: Job = {
        id: 'batch-processing-job',
        name: 'Large Scale Data Processing',
        status: 'running',
        steps: [],
        createdAt: new Date()
    };
    
    const batchStep: JobStep = {
        id: 'batch-step-001',
        jobId: batchJob.id,
        name: 'Process 1000 Records',
        status: 'running',
        createdResources: []
    };
    
    try {
        console.log('Creating 100 resources with potential duplicates...');
        const startTime = Date.now();
        
        // Create many resources, some with duplicate content
        const promises = [];
        for (let i = 0; i < 100; i++) {
            // Create some duplicate values to demonstrate deduplication
            const value = Math.floor(i / 10) * 10; // Groups of 10 with same value
            
            const promise = resourceManager.createIntegerResource(value, batchStep, batchJob);
            promises.push(promise);
        }
        
        const resources = await Promise.all(promises);
        const endTime = Date.now();
        
        console.log(`✓ Created ${resources.length} resources in ${endTime - startTime}ms`);
        
        // Analyze deduplication efficiency
        const uniqueHashes = new Set(resources.map(r => r.contentHash));
        console.log(`✓ Unique content hashes: ${uniqueHashes.size}`);
        console.log(`✓ Deduplication ratio: ${((resources.length - uniqueHashes.size) / resources.length * 100).toFixed(1)}%`);
        
        // Find all resources with specific content
        const duplicatesOf50 = await resourceManager.findResourcesByContent(
            { semanticIdentity: 50 },
            'integer'
        );
        console.log(`✓ Found ${duplicatesOf50.length} resources with value 50`);
        
    } catch (error) {
        console.error('Error in batch operations:', error);
    }
}

// Example 3: CAFS Analytics and Monitoring
async function cafsAnalytics() {
    console.log('\n=== CAFS Analytics ===');
    
    const cafs = new CAFS({
        bucketName: 'analytics-bucket',
        enableDeduplication: true
    });
    
    try {
        // Store various types of content
        const contents = [
            'Small text content',
            'Medium length content with more details and information',
            'Large content: ' + 'x'.repeat(1000),
            'Small text content', // Duplicate
            'Another small text',
            'Medium length content with more details and information', // Duplicate
        ];
        
        console.log('Storing content for analytics...');
        for (let i = 0; i < contents.length; i++) {
            await cafs.storeContent(contents[i], {
                tags: [`content-${i + 1}`, contents[i].length > 100 ? 'large' : 'small'],
                customProperties: {
                    index: i,
                    length: contents[i].length
                }
            });
        }
        
        // Analyze CAFS entries
        const allEntries = await cafs.listCAFSEntries();
        console.log(`✓ Total unique contents: ${allEntries.length}`);
        
        // Calculate storage statistics
        let totalSize = 0;
        let totalReferences = 0;
        let maxReferenceCount = 0;
        
        for (const entry of allEntries) {
            totalSize += entry.metadata.contentSize;
            totalReferences += entry.metadata.referenceCount;
            maxReferenceCount = Math.max(maxReferenceCount, entry.metadata.referenceCount);
        }
        
        console.log(`✓ Total storage used: ${totalSize} bytes`);
        console.log(`✓ Total references: ${totalReferences}`);
        console.log(`✓ Average references per content: ${(totalReferences / allEntries.length).toFixed(2)}`);
        console.log(`✓ Max references for single content: ${maxReferenceCount}`);
        
        // Find most referenced content
        const mostReferenced = allEntries.reduce((max, entry) => 
            entry.metadata.referenceCount > max.metadata.referenceCount ? entry : max
        );
        console.log(`✓ Most referenced content hash: ${mostReferenced.contentHash.substring(0, 16)}...`);
        
        // Filter by size
        const largeEntries = await cafs.listCAFSEntries(entry => entry.metadata.contentSize > 100);
        console.log(`✓ Large contents (>100 bytes): ${largeEntries.length}`);
        
        // Filter by age
        const recentEntries = await cafs.listCAFSEntries(entry => {
            const ageInMinutes = (Date.now() - entry.metadata.createdAt.getTime()) / (1000 * 60);
            return ageInMinutes < 5; // Last 5 minutes
        });
        console.log(`✓ Recent contents (last 5 min): ${recentEntries.length}`);
        
    } catch (error) {
        console.error('Error in CAFS analytics:', error);
    }
}

// Example 4: Integration with Firestore
async function firestoreIntegration() {
    console.log('\n=== Firestore Integration ===');
    
    const firestoreAdapter = new FirestoreAdapter(
        'your-project-id',
        'ml_resources',
        'ml_cafs_entries',
        'ml_jobs',
        'ml_job_steps'
    );
    
    try {
        // Create job and store in Firestore
        const job: Job = {
            id: 'firestore-integration-job',
            name: 'Firestore Integration Demo',
            status: 'running',
            steps: [],
            createdAt: new Date()
        };
        
        await firestoreAdapter.storeJob(job);
        console.log(`✓ Stored job in Firestore: ${job.id}`);
        
        // Create job step and store in Firestore
        const jobStep: JobStep = {
            id: 'firestore-step-001',
            jobId: job.id,
            name: 'Firestore Demo Step',
            status: 'running',
            createdResources: []
        };
        
        await firestoreAdapter.storeJobStep(jobStep);
        console.log(`✓ Stored job step in Firestore: ${jobStep.id}`);
        
        // Simulate resource creation and metadata storage
        const mockResource: Omit<Resource, 'content'> = {
            resourceId: {
                id: 'firestore-resource-001',
                resourceType: 'integer',
                jobStepId: jobStep.id,
                jobId: job.id,
                createdAt: new Date()
            },
            contentHash: 'abc123def456',
            metadata: {
                contentSize: 100,
                contentType: 'application/json',
                createdAt: new Date(),
                lastAccessedAt: new Date(),
                referenceCount: 1,
                tags: ['demo', 'firestore'],
                customProperties: {
                    demoProperty: 'value'
                }
            }
        };
        
        await firestoreAdapter.storeResourceMetadata(mockResource);
        console.log(`✓ Stored resource metadata in Firestore: ${mockResource.resourceId.id}`);
        
        // Retrieve and verify
        const retrievedJob = await firestoreAdapter.getJob(job.id);
        const retrievedStep = await firestoreAdapter.getJobStep(jobStep.id);
        const retrievedResource = await firestoreAdapter.getResourceMetadata(mockResource.resourceId.id);
        
        console.log(`✓ Retrieved job: ${retrievedJob?.name}`);
        console.log(`✓ Retrieved step: ${retrievedStep?.name}`);
        console.log(`✓ Retrieved resource: ${retrievedResource?.resourceId.id}`);
        
        // Query resources by job
        const jobResources = await firestoreAdapter.listResources(job.id);
        console.log(`✓ Resources for job ${job.id}: ${jobResources.length}`);
        
    } catch (error) {
        console.error('Error in Firestore integration:', error);
        console.log('Note: This example requires actual Firestore credentials');
    }
}

// Example 5: Error Recovery and Resilience
async function errorRecoveryExample() {
    console.log('\n=== Error Recovery ===');
    
    const resourceManager = new ResourceManager({
        bucketName: 'resilience-test-bucket',
        enableDeduplication: true,
        maxFileSize: 1024 // Very small for testing
    });
    
    const job: Job = {
        id: 'resilience-test-job',
        name: 'Error Recovery Test',
        status: 'running',
        steps: [],
        createdAt: new Date()
    };
    
    const jobStep: JobStep = {
        id: 'resilience-step',
        jobId: job.id,
        name: 'Test Error Scenarios',
        status: 'running',
        createdResources: []
    };
    
    // Test various error scenarios
    const testCases = [
        {
            name: 'Normal operation',
            data: { value: 42 },
            shouldSucceed: true
        },
        {
            name: 'Large content (should fail)',
            data: { value: 'x'.repeat(2000) }, // Exceeds maxFileSize
            shouldSucceed: false
        },
        {
            name: 'Normal operation after error',
            data: { value: 43 },
            shouldSucceed: true
        }
    ];
    
    for (const testCase of testCases) {
        try {
            console.log(`Testing: ${testCase.name}`);
            
            const resource = await resourceManager.createResource(
                testCase.data,
                'object',
                jobStep,
                job
            );
            
            if (testCase.shouldSucceed) {
                console.log(`✓ ${testCase.name}: Success as expected`);
            } else {
                console.log(`⚠ ${testCase.name}: Unexpectedly succeeded`);
            }
            
        } catch (error) {
            if (!testCase.shouldSucceed) {
                console.log(`✓ ${testCase.name}: Failed as expected`);
            } else {
                console.log(`✗ ${testCase.name}: Unexpected failure - ${error}`);
            }
        }
    }
}

// Run all advanced examples
async function runAdvancedExamples() {
    console.log('GCS Utils SDK - Advanced Usage Examples\n');
    
    await advancedResourceTypes();
    await batchOperationsExample();
    await cafsAnalytics();
    await firestoreIntegration();
    await errorRecoveryExample();
    
    console.log('\n=== All Advanced Examples Completed ===');
}

// Export for use in other files
export {
    advancedResourceTypes,
    batchOperationsExample,
    cafsAnalytics,
    firestoreIntegration,
    errorRecoveryExample,
    runAdvancedExamples
};

// Run examples if this file is executed directly
if (require.main === module) {
    runAdvancedExamples().catch(console.error);
}
