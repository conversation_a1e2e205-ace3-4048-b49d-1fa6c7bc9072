// test-sdk.js
const { <PERSON><PERSON><PERSON><PERSON><PERSON>, ResourceManager, CAFS } = require('./dist/index.js');

async function testBasicFunctionality() {
    console.log('🧪 Testing GCS Utils SDK...\n');
    
    try {
        // Test 1: Basic GCS Operations
        console.log('1. Testing basic GCS operations...');
        const gcsUtils = new GCSUtils(process.env.BUCKET_NAME);
        
        // Write a test file
        await gcsUtils.writeToGCS('test/basic-test.json', 42);
        console.log('✅ Written test file');
        
        // Read the test file
        const value = await gcsUtils.readFromGCS('test/basic-test.json');
        console.log(`✅ Read value: ${value}`);
        
        // Test 2: CAFS Operations
        console.log('\n2. Testing CAFS operations...');
        const cafs = new CAFS({
            bucketName: process.env.BUCKET_NAME,
            enableDeduplication: true
        });
        
        // Store content
        const result1 = await cafs.storeContent('Hello, CAFS!');
        console.log(`✅ Stored content with hash: ${result1.contentHash.substring(0, 16)}...`);
        
        // Store same content (should be deduplicated)
        const result2 = await cafs.storeContent('Hello, CAFS!');
        console.log(`✅ Deduplication test: ${result2.deduplicated ? 'PASSED' : 'FAILED'}`);
        
        // Test 3: Resource Management
        console.log('\n3. Testing Resource Management...');
        const resourceManager = new ResourceManager({
            bucketName: process.env.BUCKET_NAME
        });
        
        // Create mock job context
        const job = {
            id: 'test-job-001',
            name: 'SDK Test Job',
            status: 'running',
            steps: [],
            createdAt: new Date()
        };
        
        const jobStep = {
            id: 'test-step-001',
            jobId: job.id,
            name: 'SDK Test Step',
            status: 'running',
            createdResources: []
        };
        
        // Create integer resource
        const resource = await resourceManager.createIntegerResource(123, jobStep, job);
        console.log(`✅ Created resource: ${resource.resourceId.id}`);
        
        // Retrieve resource
        const retrieved = await resourceManager.getResource(resource.resourceId.id);
        console.log(`✅ Retrieved resource value: ${retrieved?.content.semanticIdentity}`);
        
        console.log('\n🎉 All tests passed!');
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
        process.exit(1);
    }
}

// Run the test
testBasicFunctionality();