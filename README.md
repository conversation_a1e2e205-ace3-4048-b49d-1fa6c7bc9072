# GCS Utils SDK

A comprehensive TypeScript SDK for Google Cloud Storage with Content Addressable File Storage (CAFS) support. This SDK provides efficient resource management with automatic deduplication, metadata tracking, and seamless integration with Google Cloud Platform services.

## Features

- **Content Addressable File Storage (CAFS)**: Automatic deduplication using SHA-256 hashing
- **Resource Management**: Complete lifecycle management for various resource types
- **Google Cloud Integration**: Native support for GCS and Firestore
- **Type Safety**: Full TypeScript support with comprehensive type definitions
- **Flexible Architecture**: Modular design allowing selective usage of components
- **Legacy Compatibility**: Backward compatibility with existing implementations

## Installation

```bash
npm install gcs-utils-sdk
```

## Quick Start

### Basic Usage

```typescript
import { GCSUtils, ResourceManager, CAFS } from 'gcs-utils-sdk';

// Initialize with your bucket name
const gcsUtils = new GCSUtils('your-bucket-name');

// Read/write integer values (legacy compatibility)
await gcsUtils.writeToGCS('numbers/42.json', 42);
const value = await gcsUtils.readFromGCS('numbers/42.json');
console.log(value); // 42
```

### CAFS (Content Addressable File Storage)

```typescript
import { CAFS } from 'gcs-utils-sdk';

const cafs = new CAFS({
    bucketName: 'your-bucket',
    enableDeduplication: true,
    maxFileSize: 10 * 1024 * 1024 // 10MB
});

// Store content with automatic deduplication
const result = await cafs.storeContent('Hello, World!');
console.log(result.contentHash); // SHA-256 hash
console.log(result.deduplicated); // false (first time)

// Store same content again - will be deduplicated
const result2 = await cafs.storeContent('Hello, World!');
console.log(result2.deduplicated); // true

// Retrieve content by hash
const content = await cafs.retrieveContent(result.contentHash);
console.log(content); // 'Hello, World!'
```

### Resource Management

```typescript
import { ResourceManager } from 'gcs-utils-sdk';

const resourceManager = new ResourceManager({
    bucketName: 'your-bucket',
    enableDeduplication: true
});

// Define job and job step (simplified for example)
const job = {
    id: 'job-123',
    name: 'Data Processing Job',
    status: 'running',
    steps: [],
    createdAt: new Date()
};

const jobStep = {
    id: 'step-456',
    jobId: 'job-123',
    name: 'Process Numbers',
    status: 'running',
    createdResources: []
};

// Create an integer resource
const resource = await resourceManager.createIntegerResource(42, jobStep, job);
console.log(resource.resourceId.id); // Unique resource ID
console.log(resource.contentHash); // SHA-256 hash of content

// Retrieve resource by ID
const retrieved = await resourceManager.getResource(resource.resourceId.id);
console.log(retrieved.content.semanticIdentity); // 42

// Find resources by content (demonstrates deduplication)
const duplicates = await resourceManager.findResourcesByContent(
    { semanticIdentity: 42 },
    'integer'
);
console.log(duplicates.length); // Number of resources with same content
```

## Architecture

### Core Components

1. **GCSUtils**: Low-level Google Cloud Storage operations
2. **CAFS**: Content Addressable File Storage with deduplication
3. **ResourceManager**: High-level resource lifecycle management
4. **FirestoreAdapter**: Metadata persistence in Firestore

### CAFS Implementation

The Content Addressable File Storage system provides:

- **Deduplication**: Identical content is stored only once
- **Content Addressing**: Files are addressed by their SHA-256 hash
- **Reference Counting**: Tracks how many resources reference each content
- **Automatic Cleanup**: Removes unreferenced content when reference count reaches zero

### Resource Types

Supported resource types:
- `integer`: Numeric values with semantic identity
- `string`: Text content
- `object`: JSON objects
- `array`: JSON arrays

## Configuration

### Environment Variables

```bash
BUCKET_NAME=your-gcs-bucket-name
```

### Programmatic Configuration

```typescript
import { ResourceManager, CAFS } from 'gcs-utils-sdk';

const config = {
    bucketName: 'your-bucket',
    metadataCollection: 'custom_metadata',
    enableDeduplication: true,
    maxFileSize: 50 * 1024 * 1024, // 50MB
    defaultContentType: 'application/json'
};

const resourceManager = new ResourceManager(config);
const cafs = new CAFS(config);
```

## API Reference

### GCSUtils

Core utilities for Google Cloud Storage operations.

```typescript
class GCSUtils {
    constructor(bucketName?: string)

    // Read/write integer values (legacy compatibility)
    async readFromGCS(filePath: string, options?: ReadOptions): Promise<number>
    async writeToGCS(filePath: string, value: number, options?: WriteOptions): Promise<void>

    // Raw content operations
    async readRawContent(filePath: string): Promise<string>
    async writeRawContent(filePath: string, content: string, contentType?: string): Promise<void>

    // File management
    async fileExists(filePath: string): Promise<boolean>
    async deleteFile(filePath: string): Promise<void>
    async getFileMetadata(filePath: string): Promise<any>
    async listFiles(prefix?: string): Promise<string[]>

    // Utility
    generateContentHash(content: string): string
}
```

### CAFS

Content Addressable File Storage with deduplication.

```typescript
class CAFS {
    constructor(config?: Partial<GCSUtilsConfig>)

    async storeContent(content: string, metadata?: Partial<ResourceMetadata>): Promise<CAFSOperationResult>
    async retrieveContent(contentHash: string, updateAccessTime?: boolean): Promise<string>
    async contentExists(contentHash: string): Promise<boolean>
    async deleteContent(contentHash: string, forceDelete?: boolean): Promise<void>
    async getCAFSEntry(contentHash: string): Promise<CAFSEntry | null>
    async listCAFSEntries(filter?: (entry: CAFSEntry) => boolean): Promise<CAFSEntry[]>
}
```

### ResourceManager

High-level resource management with CAFS integration.

```typescript
class ResourceManager {
    constructor(config?: Partial<GCSUtilsConfig>)

    async createResource<T>(content: T, resourceType: ResourceType, jobStep: JobStep, job: Job, metadata?: Partial<ResourceMetadata>): Promise<Resource<T>>
    async createIntegerResource(value: number, jobStep: JobStep, job: Job): Promise<Resource<IntegerInstance>>
    async getResource<T>(resourceId: string): Promise<Resource<T> | null>
    async getResourceByHash<T>(contentHash: string): Promise<Resource<T> | null>
    async findResourcesByContent<T>(content: T, resourceType: ResourceType): Promise<Resource<T>[]>
    async deleteResource(resourceId: string, forceDelete?: boolean): Promise<void>
    async listResources(jobId?: string, jobStepId?: string): Promise<Resource[]>
}
```

### FirestoreAdapter

Metadata persistence in Google Cloud Firestore.

```typescript
class FirestoreAdapter {
    constructor(projectId?: string, resourcesCollection?: string, cafsCollection?: string, jobsCollection?: string, jobStepsCollection?: string)

    // Resource operations
    async storeResourceMetadata(resource: Omit<Resource, 'content'>): Promise<void>
    async getResourceMetadata(resourceId: string): Promise<Omit<Resource, 'content'> | null>
    async updateResourceMetadata(resourceId: string, updates: Partial<ResourceMetadata>): Promise<void>
    async deleteResourceMetadata(resourceId: string): Promise<void>
    async findResourcesByContentHash(contentHash: string): Promise<Omit<Resource, 'content'>[]>
    async listResources(jobId?: string, jobStepId?: string, resourceType?: string): Promise<Omit<Resource, 'content'>[]>

    // CAFS operations
    async storeCAFSEntry(entry: CAFSEntry): Promise<void>
    async getCAFSEntry(contentHash: string): Promise<CAFSEntry | null>
    async updateCAFSEntry(contentHash: string, updates: Partial<CAFSEntry>): Promise<void>
    async deleteCAFSEntry(contentHash: string): Promise<void>
    async listCAFSEntries(): Promise<CAFSEntry[]>

    // Job operations
    async storeJob(job: Job): Promise<void>
    async getJob(jobId: string): Promise<Job | null>
    async storeJobStep(jobStep: JobStep): Promise<void>
    async getJobStep(jobStepId: string): Promise<JobStep | null>
    async addResourceToJobStep(jobStepId: string, resourceId: string): Promise<void>
}
```

## Examples

### Complete CAFS Workflow

```typescript
import { ResourceManager, Job, JobStep } from 'gcs-utils-sdk';

async function demonstrateCAFS() {
    const resourceManager = new ResourceManager({
        bucketName: 'my-resources-bucket',
        enableDeduplication: true
    });

    // Create job and job step
    const job: Job = {
        id: 'data-processing-job-001',
        name: 'Process Customer Data',
        status: 'running',
        steps: [],
        createdAt: new Date()
    };

    const jobStep: JobStep = {
        id: 'extract-numbers-step',
        jobId: job.id,
        name: 'Extract Numeric Values',
        status: 'running',
        createdResources: []
    };

    // Create multiple resources with same content
    const resource1 = await resourceManager.createIntegerResource(42, jobStep, job);
    const resource2 = await resourceManager.createIntegerResource(42, jobStep, job);

    console.log('Resource 1 hash:', resource1.contentHash);
    console.log('Resource 2 hash:', resource2.contentHash);
    console.log('Same hash?', resource1.contentHash === resource2.contentHash); // true

    // Find all resources with the same content
    const duplicates = await resourceManager.findResourcesByContent(
        { semanticIdentity: 42 },
        'integer'
    );
    console.log('Found', duplicates.length, 'resources with value 42');

    // Retrieve by different methods
    const byId = await resourceManager.getResource(resource1.resourceId.id);
    const byHash = await resourceManager.getResourceByHash(resource1.contentHash);

    console.log('Retrieved by ID:', byId?.content.semanticIdentity);
    console.log('Retrieved by hash:', byHash?.content.semanticIdentity);
}
```

### Custom Resource Types

```typescript
interface CustomData {
    name: string;
    values: number[];
    metadata: Record<string, any>;
}

async function createCustomResource() {
    const resourceManager = new ResourceManager();

    const customData: CustomData = {
        name: 'Sample Dataset',
        values: [1, 2, 3, 4, 5],
        metadata: {
            source: 'user-input',
            timestamp: new Date().toISOString()
        }
    };

    const resource = await resourceManager.createResource(
        customData,
        'object',
        jobStep,
        job,
        {
            tags: ['dataset', 'user-generated'],
            customProperties: {
                dataType: 'numeric-array',
                version: '1.0'
            }
        }
    );

    console.log('Created custom resource:', resource.resourceId.id);
    console.log('Content hash:', resource.contentHash);
}
```

## Testing

Run the test suite:

```bash
npm test
```

Run tests with coverage:

```bash
npm run test -- --coverage
```

Run tests in watch mode:

```bash
npm run test:watch
```

## Development

Build the project:

```bash
npm run build
```

Run linting:

```bash
npm run lint
```

Fix linting issues:

```bash
npm run lint:fix
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## License

MIT License - see LICENSE file for details.

## Support

For issues and questions:
- Create an issue on GitHub
- Check the documentation
- Review the test files for usage examples